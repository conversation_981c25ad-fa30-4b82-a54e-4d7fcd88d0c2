<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码修改 - VICTEL IP交换机</title>
    <link href="/CSS/weide.css" rel="stylesheet" type="text/css" />
    <style>
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .form-section h2 {
            color: #00A6A6;
            border-bottom: 2px solid #00A6A6;
            padding-bottom: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #00A6A6;
            box-shadow: 0 0 5px rgba(0,166,166,0.3);
        }
        
        .password-requirements {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .password-requirements h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 20px;
            color: #6c757d;
            font-size: 13px;
        }
        
        .password-requirements li {
            margin-bottom: 5px;
        }
        
        .password-strength {
            margin-top: 5px;
        }
        
        .strength-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: width 0.3s ease, background-color 0.3s ease;
            width: 0%;
        }
        
        .strength-weak {
            background-color: #dc3545;
        }
        
        .strength-medium {
            background-color: #ffc107;
        }
        
        .strength-strong {
            background-color: #28a745;
        }
        
        .strength-text {
            font-size: 12px;
            margin-top: 5px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            background: #00A6A6;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #008888;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .form-section {
                padding: 20px;
            }
            
            .buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-section">
            <h2>密码修改</h2>
            
            <div id="message-area"></div>
            
            <form id="password-form">
                <div class="form-group">
                    <label for="old-password">当前密码 *</label>
                    <input type="password" id="old-password" name="old-password" 
                           placeholder="请输入当前密码" required>
                </div>
                
                <div class="form-group">
                    <label for="new-password1">新密码 *</label>
                    <input type="password" id="new-password1" name="new-password1" 
                           placeholder="请输入新密码" required>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div id="strength-fill" class="strength-fill"></div>
                        </div>
                        <div id="strength-text" class="strength-text"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="new-password2">确认密码 *</label>
                    <input type="password" id="new-password2" name="new-password2" 
                           placeholder="请再次输入新密码" required>
                    <div id="password-match" style="font-size: 12px; margin-top: 5px;"></div>
                </div>
                
                <div class="password-requirements">
                    <h4>密码要求</h4>
                    <ul>
                        <li>密码长度：4-20个字符</li>
                        <li>建议包含字母、数字的组合</li>
                        <li>避免使用简单密码如：1234、admin等</li>
                        <li>新密码不能与当前密码相同</li>
                    </ul>
                </div>
                
                <div class="buttons">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        重置
                    </button>
                    <button type="submit" class="btn" id="submit-btn">
                        修改密码
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
        });
        
        // 初始化表单
        function initializeForm() {
            const form = document.getElementById('password-form');
            const newPassword1 = document.getElementById('new-password1');
            const newPassword2 = document.getElementById('new-password2');
            
            // 绑定表单提交事件
            form.addEventListener('submit', handleSubmit);
            
            // 绑定密码强度检查
            newPassword1.addEventListener('input', checkPasswordStrength);
            
            // 绑定密码匹配检查
            newPassword2.addEventListener('input', checkPasswordMatch);
            newPassword1.addEventListener('input', checkPasswordMatch);
        }
        
        // 处理表单提交
        async function handleSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const oldPassword = formData.get('old-password');
            const newPassword1 = formData.get('new-password1');
            const newPassword2 = formData.get('new-password2');
            
            // 基本验证
            if (!oldPassword || !newPassword1 || !newPassword2) {
                showMessage('请填写所有必填字段', 'error');
                return;
            }
            
            if (newPassword1 !== newPassword2) {
                showMessage('两次输入的新密码不一致', 'error');
                return;
            }
            
            if (newPassword1.length < 4 || newPassword1.length > 20) {
                showMessage('密码长度必须在4-20个字符之间', 'error');
                return;
            }
            
            if (oldPassword === newPassword1) {
                showMessage('新密码不能与当前密码相同', 'error');
                return;
            }
            
            // 禁用提交按钮
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = '修改中...';
            
            try {
                const response = await fetch('/api/v1/system/password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_password: oldPassword,
                        new_password1: newPassword1,
                        new_password2: newPassword2
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showMessage('密码修改成功！', 'success');
                    resetForm();
                } else {
                    throw new Error(data.message || '密码修改失败');
                }
            } catch (error) {
                console.error('密码修改失败:', error);
                showMessage('密码修改失败: ' + error.message, 'error');
            } finally {
                // 重新启用提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = '修改密码';
            }
        }
        
        // 检查密码强度
        function checkPasswordStrength() {
            const password = document.getElementById('new-password1').value;
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');
            
            if (!password) {
                strengthFill.style.width = '0%';
                strengthText.textContent = '';
                return;
            }
            
            let score = 0;
            let feedback = [];
            
            // 长度检查
            if (password.length >= 8) {
                score += 2;
            } else if (password.length >= 4) {
                score += 1;
                feedback.push('建议至少8位字符');
            } else {
                feedback.push('密码太短');
            }
            
            // 包含数字
            if (/\d/.test(password)) {
                score += 1;
            } else {
                feedback.push('建议包含数字');
            }
            
            // 包含小写字母
            if (/[a-z]/.test(password)) {
                score += 1;
            }
            
            // 包含大写字母
            if (/[A-Z]/.test(password)) {
                score += 1;
            } else {
                feedback.push('建议包含字母');
            }
            
            // 包含特殊字符
            if (/[^a-zA-Z0-9]/.test(password)) {
                score += 1;
            }
            
            // 避免简单密码
            const commonPasswords = ['1234', '12345', '123456', 'admin', 'password', '1111', '0000'];
            if (commonPasswords.includes(password.toLowerCase())) {
                score = Math.max(0, score - 2);
                feedback.push('避免使用简单密码');
            }
            
            // 设置强度显示
            let strengthClass = '';
            let strengthLabel = '';
            let width = 0;
            
            if (score >= 5) {
                strengthClass = 'strength-strong';
                strengthLabel = '强';
                width = 100;
            } else if (score >= 3) {
                strengthClass = 'strength-medium';
                strengthLabel = '中';
                width = 60;
            } else if (score > 0) {
                strengthClass = 'strength-weak';
                strengthLabel = '弱';
                width = 30;
            }
            
            strengthFill.className = 'strength-fill ' + strengthClass;
            strengthFill.style.width = width + '%';
            
            if (strengthLabel) {
                strengthText.textContent = `密码强度: ${strengthLabel}`;
                if (feedback.length > 0) {
                    strengthText.textContent += ` (${feedback.join(', ')})`;
                }
            } else {
                strengthText.textContent = feedback.join(', ');
            }
        }
        
        // 检查密码匹配
        function checkPasswordMatch() {
            const password1 = document.getElementById('new-password1').value;
            const password2 = document.getElementById('new-password2').value;
            const matchDiv = document.getElementById('password-match');
            
            if (!password2) {
                matchDiv.textContent = '';
                return;
            }
            
            if (password1 === password2) {
                matchDiv.textContent = '✓ 密码匹配';
                matchDiv.style.color = '#28a745';
            } else {
                matchDiv.textContent = '✗ 密码不匹配';
                matchDiv.style.color = '#dc3545';
            }
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('password-form').reset();
            document.getElementById('strength-fill').style.width = '0%';
            document.getElementById('strength-text').textContent = '';
            document.getElementById('password-match').textContent = '';
            clearMessage();
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            messageDiv.textContent = message;
            
            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);
            
            // 成功消息5秒后自动清除，错误消息保持显示
            if (type === 'success') {
                setTimeout(() => {
                    if (messageArea.contains(messageDiv)) {
                        messageArea.removeChild(messageDiv);
                    }
                }, 5000);
            }
        }
        
        // 清除消息
        function clearMessage() {
            document.getElementById('message-area').innerHTML = '';
        }
    </script>
</body>
</html> 