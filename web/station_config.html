<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基站配置 - VICTEL IP交换机</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "微软雅黑", Arial, sans-serif;
            font-size: 13px;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #00A6A6, #008888);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .config-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .panel-header {
            background: #993333;
            color: white;
            padding: 1rem 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* 移除设备类型指示器样式，因为旧项目0sci*.c中不存在硬件检测功能 */

        .panel-content {
            padding: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #555;
        }

        .form-input {
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #00A6A6;
            box-shadow: 0 0 0 2px rgba(0, 166, 166, 0.1);
        }

        .form-input.error {
            border-color: #e74c3c;
        }

        .form-select {
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            background: white;
            cursor: pointer;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.3rem;
        }

        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #00A6A6;
            color: white;
        }

        .btn-primary:hover {
            background: #008888;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #00A6A6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .section-divider {
            height: 1px;
            background: #eee;
            margin: 2rem 0;
        }

        .device-3g-only, .device-4g-only {
            display: none;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>基站功能配置</h1>
    </div>

    <div class="container">
        <div id="message" class="message"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在加载配置...</p>
        </div>

        <form id="stationConfigForm">
            <!-- 设备信息 -->
            <div class="config-panel">
                <div class="panel-header">
                    设备基本信息
                    <!-- 移除设备类型指示器，因为旧项目0sci*.c中不存在硬件检测功能 -->
                </div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="stationId">基站ID *</label>
                            <input type="number" id="stationId" name="station_id" class="form-input" 
                                   min="1" max="16777215" required>
                            <div class="error-message" id="stationIdError"></div>
                        </div>
                        <div class="form-group">
                            <label for="stationName">基站名称 *</label>
                            <input type="text" id="stationName" name="station_name" class="form-input" 
                                   maxlength="31" required>
                            <div class="error-message" id="stationNameError"></div>
                        </div>
                        <div class="form-group">
                            <label for="stationType">基站类型</label>
                            <select id="stationType" name="station_type" class="form-select">
                                <option value="0">3G模块</option>
                                <option value="1">4G模块</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="groupId">组ID</label>
                            <input type="number" id="groupId" name="group_id" class="form-input" 
                                   min="1" max="16777215">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网络配置 -->
            <div class="config-panel">
                <div class="panel-header">网络配置</div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="centerIp">中心服务器IP *</label>
                            <input type="text" id="centerIp" name="center_ip" class="form-input" 
                                   pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$" required>
                            <div class="error-message" id="centerIpError"></div>
                        </div>
                        <div class="form-group">
                            <label for="centerPort">中心服务器端口 *</label>
                            <input type="number" id="centerPort" name="center_port" class="form-input" 
                                   min="1" max="65535" required>
                        </div>
                        <div class="form-group">
                            <label for="localIp">本地IP地址</label>
                            <input type="text" id="localIp" name="local_ip" class="form-input" 
                                   pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                        </div>
                        <div class="form-group">
                            <label for="localPort">本地端口</label>
                            <input type="number" id="localPort" name="local_port" class="form-input" 
                                   min="1" max="65535">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 无线配置 -->
            <div class="config-panel">
                <div class="panel-header">无线配置</div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="signalStrength">信号强度等级</label>
                            <select id="signalStrength" name="signal_strength" class="form-select">
                                <option value="1">1 (最低)</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5 (中等)</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10 (最强)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="voiceCodec">语音编码类型</label>
                            <select id="voiceCodec" name="voice_codec" class="form-select">
                                <option value="0">G.711 A-law</option>
                                <option value="1">G.711 μ-law</option>
                                <option value="2">G.729</option>
                                <option value="3">AMR</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="powerLevel">功率等级</label>
                            <select id="powerLevel" name="power_level" class="form-select">
                                <option value="1">1 (最低)</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5 (最高)</option>
                            </select>
                        </div>
                        <div class="form-group device-3g-only">
                            <label for="frequencyBand">3G频段设置</label>
                            <select id="frequencyBand" name="frequency_band" class="form-select">
                                <option value="900">900 MHz</option>
                                <option value="1800">1800 MHz</option>
                                <option value="2100">2100 MHz</option>
                            </select>
                        </div>
                        <div class="form-group device-4g-only">
                            <label for="networkMode">4G网络模式</label>
                            <select id="networkMode" name="network_mode" class="form-select">
                                <option value="0">GSM</option>
                                <option value="1">WCDMA</option>
                                <option value="2">LTE</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统配置 -->
            <div class="config-panel">
                <div class="panel-header">系统配置</div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="heartbeatInterval">心跳间隔（秒）</label>
                            <input type="number" id="heartbeatInterval" name="heartbeat_interval" 
                                   class="form-input" min="1" max="300" value="30">
                        </div>
                        <div class="form-group">
                            <label for="autoRegister">自动注册</label>
                            <select id="autoRegister" name="auto_register" class="form-select">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="moduleVersion">模块版本</label>
                            <input type="text" id="moduleVersion" name="module_version" class="form-input" 
                                   maxlength="15" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                <button type="submit" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <script>
        // 移除设备类型检测功能，因为旧项目0sci*.c中不存在硬件检测功能
        // 保持与旧项目100%功能一致，不添加额外的硬件检测功能

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStationConfig();

            // 设备类型改变时切换界面（保留手动选择功能）
            document.getElementById('stationType').addEventListener('change', function() {
                switchDeviceTypeUI(this.value);
            });
        });

        // 移除设备类型检测函数，因为旧项目0sci*.c中不存在硬件检测功能
        // 保持与旧项目100%功能一致，不添加额外的硬件检测功能

        // 切换设备类型UI
        function switchDeviceTypeUI(deviceType) {
            const elements3G = document.querySelectorAll('.device-3g-only');
            const elements4G = document.querySelectorAll('.device-4g-only');
            
            if (deviceType == 0) { // 3G
                elements3G.forEach(el => el.style.display = 'flex');
                elements4G.forEach(el => el.style.display = 'none');
            } else { // 4G
                elements3G.forEach(el => el.style.display = 'none');
                elements4G.forEach(el => el.style.display = 'flex');
            }
        }

        // 加载基站配置
        async function loadStationConfig() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/v1/config/device/station');
                const result = await response.json();
                
                if (result.code === 200) {
                    populateForm(result.data);
                    showMessage('配置加载成功', 'success');
                } else {
                    showMessage(result.message || '配置加载失败', 'error');
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                showMessage('配置加载失败，请检查网络连接', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 填充表单数据
        function populateForm(data) {
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data[key];
                }
            });
            
            // 转换IP地址
            if (data.center_ip) {
                document.getElementById('centerIp').value = intToIp(data.center_ip);
            }
            if (data.local_ip) {
                document.getElementById('localIp').value = intToIp(data.local_ip);
            }
        }

        // 表单提交处理
        document.getElementById('stationConfigForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            const formData = collectFormData();
            
            try {
                showLoading(true);
                
                const response = await fetch('/api/v1/config/device/station', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('基站配置保存成功', 'success');
                } else {
                    showMessage(result.message || '配置保存失败', 'error');
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showMessage('配置保存失败，请检查网络连接', 'error');
            } finally {
                showLoading(false);
            }
        });

        // 收集表单数据
        function collectFormData() {
            const formData = {};
            const form = document.getElementById('stationConfigForm');
            const inputs = form.querySelectorAll('input, select');
            
            inputs.forEach(input => {
                const name = input.name;
                let value = input.value;
                
                if (name === 'center_ip' || name === 'local_ip') {
                    value = ipToInt(value);
                } else if (input.type === 'number') {
                    value = parseInt(value) || 0;
                }
                
                if (name) {
                    formData[name] = value;
                }
            });
            
            return formData;
        }

        // 表单验证
        function validateForm() {
            let isValid = true;
            
            // 验证基站ID
            const stationId = document.getElementById('stationId');
            if (!stationId.value || stationId.value < 1 || stationId.value > 16777215) {
                showFieldError('stationIdError', '基站ID必须在1-16777215范围内');
                isValid = false;
            } else {
                clearFieldError('stationIdError');
            }
            
            // 验证基站名称
            const stationName = document.getElementById('stationName');
            if (!stationName.value.trim()) {
                showFieldError('stationNameError', '基站名称不能为空');
                isValid = false;
            } else {
                clearFieldError('stationNameError');
            }
            
            // 验证中心服务器IP
            const centerIp = document.getElementById('centerIp');
            if (!isValidIP(centerIp.value)) {
                showFieldError('centerIpError', '请输入有效的IP地址');
                isValid = false;
            } else {
                clearFieldError('centerIpError');
            }
            
            return isValid;
        }

        // 显示字段错误
        function showFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = message;
            inputElement.classList.add('error');
        }

        // 清除字段错误
        function clearFieldError(errorId) {
            const errorElement = document.getElementById(errorId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = '';
            inputElement.classList.remove('error');
        }

        // IP地址验证
        function isValidIP(ip) {
            const pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return pattern.test(ip);
        }

        // IP地址转换函数
        function ipToInt(ip) {
            if (!ip) return 0;
            const parts = ip.split('.');
            return (parseInt(parts[0]) << 24) + (parseInt(parts[1]) << 16) + 
                   (parseInt(parts[2]) << 8) + parseInt(parts[3]);
        }

        function intToIp(int) {
            return [(int >>> 24) & 255, (int >>> 16) & 255, 
                   (int >>> 8) & 255, int & 255].join('.');
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置所有配置吗？')) {
                document.getElementById('stationConfigForm').reset();
                loadStationConfig();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 显示消息
        function showMessage(text, type) {
            const messageElement = document.getElementById('message');
            messageElement.textContent = text;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
