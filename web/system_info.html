<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统信息管理 - VICTEL IP交换机</title>
    <link href="/CSS/weide.css" rel="stylesheet" type="text/css" />
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .section h2 {
            color: #00A6A6;
            border-bottom: 2px solid #00A6A6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: bold;
            color: #333;
        }
        
        .info-value {
            color: #666;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #4CAF50;
        }
        
        .status-offline {
            background-color: #f44336;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #00A6A6;
            transition: width 0.3s ease;
        }
        
        .progress-high {
            background-color: #f44336;
        }
        
        .progress-medium {
            background-color: #ff9800;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .btn {
            background: #00A6A6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #008888;
        }
        
        .btn-danger {
            background: #993333;
        }
        
        .btn-danger:hover {
            background: #cc4444;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 300px;
            text-align: center;
        }
        
        .modal-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #00A6A6; text-align: center; margin-bottom: 30px;">
            系统信息管理
        </h1>
        
        <!-- 系统信息部分 -->
        <div class="section">
            <h2>系统信息</h2>
            <div id="system-info-loading" class="loading">
                正在加载系统信息...
            </div>
            <div id="system-info-content" style="display: none;">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">设备类型:</span>
                        <span class="info-value" id="device-type">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">系统版本:</span>
                        <span class="info-value" id="system-version">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">固件版本:</span>
                        <span class="info-value" id="firmware-version">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">硬件版本:</span>
                        <span class="info-value" id="hardware-version">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">编译日期:</span>
                        <span class="info-value" id="build-date">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">编译时间:</span>
                        <span class="info-value" id="build-time">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">MAC地址:</span>
                        <span class="info-value" id="mac-address">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CPU信息:</span>
                        <span class="info-value" id="cpu-info">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">启动时间:</span>
                        <span class="info-value" id="boot-time">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">运行时间:</span>
                        <span class="info-value" id="uptime">-</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统状态部分 -->
        <div class="section">
            <h2>系统状态</h2>
            <div id="system-status-content">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">网络状态:</span>
                        <span class="info-value">
                            <span id="network-status-indicator" class="status-indicator"></span>
                            <span id="network-status-text">检测中...</span>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CPU使用率:</span>
                        <div class="info-value">
                            <span id="cpu-usage-text">0%</span>
                            <div class="progress-bar">
                                <div id="cpu-usage-bar" class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">内存使用率:</span>
                        <div class="info-value">
                            <span id="memory-usage-text">0%</span>
                            <div class="progress-bar">
                                <div id="memory-usage-bar" class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">存储使用率:</span>
                        <div class="info-value">
                            <span id="storage-usage-text">0%</span>
                            <div class="progress-bar">
                                <div id="storage-usage-bar" class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CPU温度:</span>
                        <span class="info-value" id="cpu-temperature">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">总内存:</span>
                        <span class="info-value" id="total-memory">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">可用内存:</span>
                        <span class="info-value" id="free-memory">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">总存储:</span>
                        <span class="info-value" id="total-storage">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">可用存储:</span>
                        <span class="info-value" id="free-storage">-</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="section">
            <h2>系统操作</h2>
            <div id="message-area"></div>
            <div class="buttons">
                <button id="refresh-btn" class="btn" onclick="loadSystemInfo()">
                    刷新信息
                </button>
                <button id="reboot-btn" class="btn btn-danger" onclick="showRebootConfirm()">
                    系统重启
                </button>
                <button id="reset-btn" class="btn btn-danger" onclick="showResetConfirm()">
                    重置配置
                </button>
            </div>
        </div>
    </div>
    
    <!-- 重启确认对话框 -->
    <div id="reboot-modal" class="modal">
        <div class="modal-content">
            <h3>确认重启</h3>
            <p>确定要重启系统吗？</p>
            <p style="color: #993333; font-size: 12px;">重启后将断开所有连接</p>
            <div class="modal-buttons">
                <button class="btn" onclick="hideModal('reboot-modal')">取消</button>
                <button class="btn btn-danger" onclick="rebootSystem()">确认重启</button>
            </div>
        </div>
    </div>
    
    <!-- 重置确认对话框 -->
    <div id="reset-modal" class="modal">
        <div class="modal-content">
            <h3>确认重置</h3>
            <p>确定要重置所有配置吗？</p>
            <p style="color: #993333; font-size: 12px;">此操作将删除所有配置文件并重启系统</p>
            <div class="modal-buttons">
                <button class="btn" onclick="hideModal('reset-modal')">取消</button>
                <button class="btn btn-danger" onclick="resetConfig()">确认重置</button>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let systemInfo = null;
        let systemStatus = null;
        let refreshInterval = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemInfo();
            startAutoRefresh();
        });
        
        // 自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(loadSystemInfo, 30000); // 30秒刷新一次
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
        
        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const response = await fetch('/api/v1/system/info', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.code === 200) {
                    systemInfo = data.data.system_info;
                    systemStatus = data.data.system_status;
                    updateSystemInfoDisplay();
                    updateSystemStatusDisplay();
                } else {
                    throw new Error(data.message || '获取系统信息失败');
                }
            } catch (error) {
                console.error('加载系统信息失败:', error);
                showMessage('加载系统信息失败: ' + error.message, 'error');
            }
        }
        
        // 更新系统信息显示
        function updateSystemInfoDisplay() {
            if (!systemInfo) return;
            
            document.getElementById('system-info-loading').style.display = 'none';
            document.getElementById('system-info-content').style.display = 'block';
            
            document.getElementById('device-type').textContent = systemInfo.device_type || '-';
            document.getElementById('system-version').textContent = systemInfo.system_version || '-';
            document.getElementById('firmware-version').textContent = systemInfo.firmware_version || '-';
            document.getElementById('hardware-version').textContent = systemInfo.hardware_version || '-';
            document.getElementById('build-date').textContent = systemInfo.build_date || '-';
            document.getElementById('build-time').textContent = systemInfo.build_time || '-';
            document.getElementById('mac-address').textContent = systemInfo.mac_address || '-';
            document.getElementById('cpu-info').textContent = systemInfo.cpu_info || '-';
            
            // 格式化启动时间
            if (systemInfo.boot_time) {
                const bootTime = new Date(systemInfo.boot_time * 1000);
                document.getElementById('boot-time').textContent = bootTime.toLocaleString();
            }
            
            // 格式化运行时间
            if (systemInfo.uptime) {
                document.getElementById('uptime').textContent = formatUptime(systemInfo.uptime);
            }
        }
        
        // 更新系统状态显示
        function updateSystemStatusDisplay() {
            if (!systemStatus) return;
            
            // 网络状态
            const networkIndicator = document.getElementById('network-status-indicator');
            const networkText = document.getElementById('network-status-text');
            if (systemStatus.network_status) {
                networkIndicator.className = 'status-indicator status-online';
                networkText.textContent = '在线';
            } else {
                networkIndicator.className = 'status-indicator status-offline';
                networkText.textContent = '离线';
            }
            
            // CPU使用率
            updateProgressBar('cpu-usage', systemStatus.cpu_usage || 0);
            
            // 内存使用率
            updateProgressBar('memory-usage', systemStatus.memory_usage || 0);
            
            // 存储使用率
            updateProgressBar('storage-usage', systemStatus.storage_usage || 0);
            
            // CPU温度
            if (systemStatus.temperature) {
                document.getElementById('cpu-temperature').textContent = `${systemStatus.temperature}°C`;
            }
            
            // 内存信息
            if (systemInfo) {
                document.getElementById('total-memory').textContent = formatSize(systemInfo.memory_total * 1024);
                document.getElementById('free-memory').textContent = formatSize(systemInfo.memory_free * 1024);
                document.getElementById('total-storage').textContent = formatSize(systemInfo.storage_total * 1024);
                document.getElementById('free-storage').textContent = formatSize(systemInfo.storage_free * 1024);
            }
        }
        
        // 更新进度条
        function updateProgressBar(prefix, percentage) {
            const textElement = document.getElementById(`${prefix}-text`);
            const barElement = document.getElementById(`${prefix}-bar`);
            
            textElement.textContent = `${percentage}%`;
            barElement.style.width = `${percentage}%`;
            
            // 设置颜色
            barElement.className = 'progress-fill';
            if (percentage >= 80) {
                barElement.classList.add('progress-high');
            } else if (percentage >= 60) {
                barElement.classList.add('progress-medium');
            }
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            let result = '';
            if (days > 0) result += `${days}天 `;
            if (hours > 0) result += `${hours}小时 `;
            if (minutes > 0) result += `${minutes}分钟`;
            
            return result || '少于1分钟';
        }
        
        // 格式化文件大小
        function formatSize(bytes) {
            if (bytes === 0) return '0 B';
            
            const units = ['B', 'KB', 'MB', 'GB', 'TB'];
            const k = 1024;
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + units[i];
        }
        
        // 显示重启确认对话框
        function showRebootConfirm() {
            document.getElementById('reboot-modal').style.display = 'block';
        }
        
        // 显示重置确认对话框
        function showResetConfirm() {
            document.getElementById('reset-modal').style.display = 'block';
        }
        
        // 隐藏对话框
        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // 系统重启
        async function rebootSystem() {
            hideModal('reboot-modal');
            
            try {
                const response = await fetch('/api/v1/system/reboot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showMessage('系统重启指令已发送，设备即将重启...', 'success');
                    stopAutoRefresh();
                    
                    // 禁用所有按钮
                    document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
                } else {
                    throw new Error(data.message || '系统重启失败');
                }
            } catch (error) {
                console.error('系统重启失败:', error);
                showMessage('系统重启失败: ' + error.message, 'error');
            }
        }
        
        // 重置配置
        async function resetConfig() {
            hideModal('reset-modal');
            
            try {
                // 重置配置通常需要发送特定的操作参数
                const response = await fetch('/api/v1/system/reboot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        operation: 'reset_config'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showMessage('配置重置指令已发送，设备即将重启...', 'success');
                    stopAutoRefresh();
                    
                    // 禁用所有按钮
                    document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
                } else {
                    throw new Error(data.message || '配置重置失败');
                }
            } catch (error) {
                console.error('配置重置失败:', error);
                showMessage('配置重置失败: ' + error.message, 'error');
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            
            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);
            
            // 5秒后自动清除消息
            setTimeout(() => {
                if (messageArea.contains(messageDiv)) {
                    messageArea.removeChild(messageDiv);
                }
            }, 5000);
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html> 