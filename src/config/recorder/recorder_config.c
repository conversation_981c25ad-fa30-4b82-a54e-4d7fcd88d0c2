/*******************************************************************
 * File          : recorder_config.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 录音基站配置模块实现 - 基于deprecated/cgi/0mini.c和0recorder.c重构
 * 支持录音模块和最小基站两种设备类型，确保100%兼容性
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : 从recorder_base_config.c重构，遵循center模块组织模式
 *******************************************************************/

#include "recorder_config.h"
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <errno.h>
#include <regex.h>
#include "utils/file_utils.h"
#include "utils/network_utils.h"

// 配置文件路径 - 基于deprecated/cgi/inc/0define.h
#define COMMONCFG       "/home/<USER>/cfg/common.cfg"
#define BOARDCFG        "/home/<USER>/cfg/board.cfg"
#define CONFERENCECFG   "/home/<USER>/cfg/conferece.cfg"
#define ETHERNETCFG     "/etc/eth0-setting"

// 配置文件长度和地址定义
#define FILE_LEN_OF_COMMONCFG    4096
#define FILE_LEN_OF_BOARDCFG     4096
#define FILE_LEN_OF_CONFIGURECFG 4096

#define START_ADDR_COMMONCFG     0
#define START_ADDR_BOARD_BASIC   256
#define START_ADDR_CONF          512
#define START_ADDR_CONF_PEER     768
#define LEN_RESV_CONF_PEER       64

#define PEER_MAX                 8

// 设备类型定义 - 基于deprecated/cgi/inc/0define.h
#define eTypeRecorder   0x13
#define eTypeMini       0x17

// 静态函数声明
static int read_configure(const char *filename, uint32_t offset, size_t size, void *data);
static int write_configure(const char *filename, uint32_t offset, size_t size, const void *data);
static int read_eth_config(recorder_board_net_t *pBoard, int index);
static int write_eth_config(const recorder_board_net_t *pBoard, int index);

/**
 * 验证录音基站配置数据
 */
int validate_recorder_config(const recorder_config_json_t *config) {
    if (!config) {
        return -1;
    }
    
    // 验证IP地址格式
    struct in_addr addr;
    if (inet_aton(config->ip, &addr) == 0) {
        return -1;
    }
    if (inet_aton(config->mask, &addr) == 0) {
        return -1;
    }
    if (inet_aton(config->gateway, &addr) == 0) {
        return -1;
    }
    if (inet_aton(config->dns, &addr) == 0) {
        return -1;
    }
    
    // 验证端口范围
    if (config->daemon_port == 0 || config->daemon_port > 65535) {
        return -1;
    }
    if (config->log_port == 0 || config->log_port > 65535) {
        return -1;
    }
    if (config->data_listen_port == 0 || config->data_listen_port > 65535) {
        return -1;
    }
    if (config->data_send_port == 0 || config->data_send_port > 65535) {
        return -1;
    }
    if (config->vbus_base_port == 0 || config->vbus_base_port > 65535) {
        return -1;
    }
    
    // 验证设备类型
    if (config->device_type != RECORDER_DEVICE_TYPE_RECORDER && 
        config->device_type != RECORDER_DEVICE_TYPE_MINI) {
        return -1;
    }
    
    // 验证网络地址（24位）
    if (config->net_address > 0xFFFFFF) {
        return -1;
    }
    
    return 0;
}

/**
 * JSON配置转换为二进制配置
 */
int convert_recorder_json_to_binary(const recorder_config_json_t *json_config, 
                                   recorder_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    memset(binary_config, 0, sizeof(recorder_config_binary_t));
    
    // 网络配置转换
    strncpy(binary_config->board_net.ethconfig.ip, json_config->ip, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.mask, json_config->mask, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.gateway, json_config->gateway, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.dns, json_config->dns, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.mac, json_config->mac, LEN_MAC_ADDR-1);
    binary_config->board_net.wlan_enable_val = 0;
    
    // 转换为数字格式
    eth_str_to_num(&binary_config->board_net.ethconfig, &binary_config->net_config);
    
    // 通用配置
    binary_config->common_config.ds = 1;
    binary_config->common_config.sw = 1;
    binary_config->common_config.conf_num = 4;
    binary_config->common_config.normal_num = 16;
    
    // 板卡基础配置
    binary_config->board_basic.daemon_ip = ip_str_to_num(json_config->daemon_ip);
    binary_config->board_basic.daemon_port = json_config->daemon_port;
    binary_config->board_basic.log_ip = ip_str_to_num(json_config->log_ip);
    binary_config->board_basic.log_port = json_config->log_port;
    binary_config->board_basic.cfg_ip = binary_config->net_config.ip;
    binary_config->board_basic.cfg_port = 80;
    binary_config->board_basic.log_level = json_config->log_level;
    binary_config->board_basic.log_to_where = json_config->log_to_where;
    binary_config->board_basic.data_listen_port = json_config->data_listen_port;
    binary_config->board_basic.data_send_port = json_config->data_send_port;
    
    // 会议配置
    binary_config->conf_config.get_cfg_method = json_config->get_cfg_method;
    binary_config->conf_config.network_mode = json_config->network_mode;
    binary_config->conf_config.voice_ip = binary_config->net_config.ip;
    binary_config->conf_config.data_listen_port = json_config->data_listen_port;
    binary_config->conf_config.vbus_base_port = json_config->vbus_base_port;
    binary_config->conf_config.net_address = json_config->net_address & 0xFFFFFF;
    binary_config->conf_config.base_type = json_config->base_type;
    binary_config->conf_config.vcan_number = json_config->vcan_number;
    binary_config->conf_config.buffertime = json_config->buffertime;
    binary_config->conf_config.downtime = json_config->downtime;
    binary_config->conf_config.resettime = json_config->resettime;
    binary_config->conf_config.peer_base_num = json_config->peer_base_num;
    binary_config->conf_config.spec_function = json_config->spec_function;
    
    // 对端基站配置
    binary_config->peer_config.peer_ip = binary_config->net_config.ip;
    binary_config->peer_config.peer_voice_ip = binary_config->net_config.ip;
    binary_config->peer_config.peer_data_listen_port = json_config->data_listen_port;
    binary_config->peer_config.peer_voice_port_base = json_config->vbus_base_port;
    binary_config->peer_config.peer_net_address = json_config->net_address & 0xFFFFFF;
    binary_config->peer_config.peer_type = json_config->device_type;
    
    // 设备类型
    binary_config->device_type = json_config->device_type;
    
    return 0;
}

/**
 * 二进制配置转换为JSON配置
 */
int convert_recorder_binary_to_json(const recorder_config_binary_t *binary_config, 
                                   recorder_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    memset(json_config, 0, sizeof(recorder_config_json_t));
    
    // 网络配置转换
    strncpy(json_config->ip, binary_config->board_net.ethconfig.ip, LEN_IP_ADDR-1);
    strncpy(json_config->mask, binary_config->board_net.ethconfig.mask, LEN_IP_ADDR-1);
    strncpy(json_config->gateway, binary_config->board_net.ethconfig.gateway, LEN_IP_ADDR-1);
    strncpy(json_config->dns, binary_config->board_net.ethconfig.dns, LEN_IP_ADDR-1);
    strncpy(json_config->mac, binary_config->board_net.ethconfig.mac, LEN_MAC_ADDR-1);
    
    // 板卡基础配置
    ip_num_to_str(binary_config->board_basic.daemon_ip, json_config->daemon_ip, LEN_IP_ADDR);
    json_config->daemon_port = binary_config->board_basic.daemon_port;
    ip_num_to_str(binary_config->board_basic.log_ip, json_config->log_ip, LEN_IP_ADDR);
    json_config->log_port = binary_config->board_basic.log_port;
    json_config->log_level = binary_config->board_basic.log_level;
    json_config->log_to_where = binary_config->board_basic.log_to_where;
    json_config->data_listen_port = binary_config->board_basic.data_listen_port;
    json_config->data_send_port = binary_config->board_basic.data_send_port;
    
    // 会议配置
    json_config->get_cfg_method = binary_config->conf_config.get_cfg_method;
    json_config->network_mode = binary_config->conf_config.network_mode;
    json_config->vbus_base_port = binary_config->conf_config.vbus_base_port;
    json_config->net_address = binary_config->conf_config.net_address;
    json_config->base_type = binary_config->conf_config.base_type;
    json_config->vcan_number = binary_config->conf_config.vcan_number;
    json_config->buffertime = binary_config->conf_config.buffertime;
    json_config->downtime = binary_config->conf_config.downtime;
    json_config->resettime = binary_config->conf_config.resettime;
    json_config->peer_base_num = binary_config->conf_config.peer_base_num;
    json_config->spec_function = binary_config->conf_config.spec_function;
    
    // 设备类型
    json_config->device_type = binary_config->device_type;
    
    return 0;
}

/**
 * 读取录音基站配置文件
 */
int read_recorder_config_file(recorder_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    int ret;
    int checkret = 0;
    
    // 读取网络配置
    ret = read_eth_config(&config->board_net, 0);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_ETH_ERR;
    }
    
    // 读取通用配置
    ret = read_file_with_offset(COMMONCFG, START_ADDR_COMMONCFG,
                        sizeof(recorder_cfg_common_t), &config->common_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_COMMON_ERR;
    }
    
    // 读取板卡基础配置
    ret = read_file_with_offset(BOARDCFG, START_ADDR_BOARD_BASIC,
                        sizeof(recorder_cfg_board_basic_t), &config->board_basic);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_BASIC_ERR;
    }
    
    // 读取会议配置
    ret = read_file_with_offset(CONFERENCECFG, START_ADDR_CONF,
                        sizeof(recorder_cfg_conf_t), &config->conf_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_CONF_ERR;
    }
    
    // 读取对端基站配置
    ret = read_file_with_offset(CONFERENCECFG, START_ADDR_CONF_PEER,
                        sizeof(recorder_cfg_peer_base_t), &config->peer_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_CONF_ERR;
    }
    
    // 转换网络配置
    eth_str_to_num(&config->board_net.ethconfig, &config->net_config);
    
    return checkret;
}

/**
 * 写入录音基站配置文件
 */
int write_recorder_config_file(const recorder_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    int ret;
    int checkret = 0;
    
    // 写入网络配置
    ret = write_eth_config(&config->board_net, 0);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_ETH_ERR;
    }
    
    // 写入通用配置
    ret = write_file_with_offset(COMMONCFG, START_ADDR_COMMONCFG,
                         sizeof(recorder_cfg_common_t), &config->common_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_COMMON_ERR;
    }
    
    // 写入板卡基础配置
    ret = write_file_with_offset(BOARDCFG, START_ADDR_BOARD_BASIC,
                         sizeof(recorder_cfg_board_basic_t), &config->board_basic);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_BASIC_ERR;
    }
    
    // 写入会议配置
    ret = write_file_with_offset(CONFERENCECFG, START_ADDR_CONF,
                         sizeof(recorder_cfg_conf_t), &config->conf_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_CONF_ERR;
    }
    
    // 写入对端基站配置
    ret = write_file_with_offset(CONFERENCECFG, START_ADDR_CONF_PEER,
                         sizeof(recorder_cfg_peer_base_t), &config->peer_config);
    if (ret < 0) {
        checkret |= RECORDER_OPEN_CONF_ERR;
    }
    
    if (checkret == 0) {
        checkret = RECORDER_SAVE_SUCCESS;
    }

    return checkret;
}

/**
 * 设置默认录音基站配置
 */
void set_default_recorder_config(recorder_config_binary_t *config) {
    if (!config) {
        return;
    }

    memset(config, 0, sizeof(recorder_config_binary_t));

    // 默认网络配置
    strcpy(config->board_net.ethconfig.ip, "***********00");
    strcpy(config->board_net.ethconfig.mask, "*************");
    strcpy(config->board_net.ethconfig.gateway, "***********");
    strcpy(config->board_net.ethconfig.dns, "*******");
    strcpy(config->board_net.ethconfig.mac, "00:11:22:33:44:55");
    config->board_net.wlan_enable_val = 0;

    // 转换为数字格式
    eth_str_to_num(&config->board_net.ethconfig, &config->net_config);

    // 默认通用配置
    config->common_config.ds = 1;
    config->common_config.sw = 1;
    config->common_config.conf_num = 4;
    config->common_config.normal_num = 16;

    // 默认板卡基础配置
    config->board_basic.daemon_ip = config->net_config.ip;
    config->board_basic.daemon_port = 2500;
    config->board_basic.log_ip = config->net_config.ip;
    config->board_basic.log_port = 514;
    config->board_basic.cfg_ip = config->net_config.ip;
    config->board_basic.cfg_port = 80;
    config->board_basic.log_level = 3;
    config->board_basic.log_to_where = 0;
    config->board_basic.data_listen_port = 2600;
    config->board_basic.data_send_port = 2601;

    // 默认会议配置
    config->conf_config.get_cfg_method = 0;
    config->conf_config.network_mode = 0;
    config->conf_config.voice_ip = config->net_config.ip;
    config->conf_config.data_listen_port = 2600;
    config->conf_config.vbus_base_port = 3000;
    config->conf_config.net_address = 0x000001;
    config->conf_config.base_type = eTypeRecorder;
    config->conf_config.vcan_number = 1;
    config->conf_config.buffertime = 5000;
    config->conf_config.downtime = 30000;
    config->conf_config.resettime = 60;
    config->conf_config.peer_base_num = 1;
    config->conf_config.spec_function = 0;

    // 默认对端基站配置
    config->peer_config.peer_ip = config->net_config.ip;
    config->peer_config.peer_voice_ip = config->net_config.ip;
    config->peer_config.peer_data_listen_port = 2600;
    config->peer_config.peer_voice_port_base = 3000;
    config->peer_config.peer_net_address = 0x000001;
    config->peer_config.peer_type = eTypeRecorder;

    // 默认设备类型
    config->device_type = RECORDER_DEVICE_TYPE_RECORDER;
}

/**
 * 根据设备类型读取配置
 */
int read_recorder_base_config(recorder_config_binary_t *config, recorder_device_type_t device_type) {
    if (!config) {
        return -1;
    }

    int ret = read_recorder_config_file(config);
    if (ret < 0) {
        // 使用默认配置
        set_default_recorder_config(config);
        config->device_type = device_type;
        config->conf_config.base_type = device_type;
        config->peer_config.peer_type = device_type;
    }

    return ret;
}

/**
 * 根据设备类型写入配置
 */
int write_recorder_base_config(const recorder_config_binary_t *config, recorder_device_type_t device_type) {
    if (!config) {
        return -1;
    }

    // 创建临时配置副本并设置设备类型
    recorder_config_binary_t temp_config = *config;
    temp_config.device_type = device_type;
    temp_config.conf_config.base_type = device_type;
    temp_config.peer_config.peer_type = device_type;

    return write_recorder_config_file(&temp_config);
}

// ===================== 工具函数实现 =====================

/**
 * 获取录音设备类型名称
 */
const char* get_recorder_device_name(recorder_device_type_t device_type) {
    switch (device_type) {
        case RECORDER_DEVICE_TYPE_RECORDER:
            return "录音模块";
        case RECORDER_DEVICE_TYPE_MINI:
            return "最小基站";
        default:
            return "未知设备";
    }
}

/**
 * 读取网络配置 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
static int read_eth_config(recorder_board_net_t *pBoard, int index) {
    if (!pBoard) {
        return -1;
    }

    // TODO: 实现网络配置文件读取
    // 这里暂时返回错误，让调用者使用默认值
    return -1;
}

/**
 * 写入网络配置 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
static int write_eth_config(const recorder_board_net_t *pBoard, int index) {
    if (!pBoard) {
        return -1;
    }

    // TODO: 实现网络配置文件写入
    // 这里暂时返回成功
    return 0;
}
