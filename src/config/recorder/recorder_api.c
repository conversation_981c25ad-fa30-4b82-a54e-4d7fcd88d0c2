/*******************************************************************
 * File          : recorder_api.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 录音基站配置API处理模块 - 基于deprecated/cgi/0mini.c和0recorder.c重构
 * 提供RESTful API接口处理录音基站配置的读取和写入
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : 从config_handler.c迁移录音基站相关API处理逻辑
 *******************************************************************/

#include "recorder_config.h"
#include "core/response.h"
#include <stdio.h>
#include <string.h>
#include <microhttpd.h>

/**
 * 处理录音基站配置获取请求
 * GET /api/v1/config/device/recorder
 */
int recorder_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    recorder_config_binary_t binary_config;
    recorder_config_json_t json_config;
    
    // 从URL中提取设备类型（如果有）
    recorder_device_type_t device_type = RECORDER_DEVICE_TYPE_RECORDER;
    if (strstr(url, "mini")) {
        device_type = RECORDER_DEVICE_TYPE_MINI;
    }
    
    // 读取录音基站配置文件
    if (read_recorder_base_config(&binary_config, device_type) < 0) {
        // 使用默认配置
        set_default_recorder_config(&binary_config);
        binary_config.device_type = device_type;
    }
    
    // 转换为JSON格式
    if (convert_recorder_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert recorder configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    
    // 网络配置
    cJSON *network = cJSON_CreateObject();
    cJSON_AddStringToObject(network, "ip", json_config.ip);
    cJSON_AddStringToObject(network, "mask", json_config.mask);
    cJSON_AddStringToObject(network, "gateway", json_config.gateway);
    cJSON_AddStringToObject(network, "dns", json_config.dns);
    cJSON_AddStringToObject(network, "mac", json_config.mac);
    cJSON_AddItemToObject(json_data, "network", network);
    
    // 基础配置
    cJSON *basic = cJSON_CreateObject();
    cJSON_AddStringToObject(basic, "daemon_ip", json_config.daemon_ip);
    cJSON_AddNumberToObject(basic, "daemon_port", json_config.daemon_port);
    cJSON_AddStringToObject(basic, "log_ip", json_config.log_ip);
    cJSON_AddNumberToObject(basic, "log_port", json_config.log_port);
    cJSON_AddNumberToObject(basic, "log_level", json_config.log_level);
    cJSON_AddNumberToObject(basic, "log_to_where", json_config.log_to_where);
    cJSON_AddNumberToObject(basic, "data_listen_port", json_config.data_listen_port);
    cJSON_AddNumberToObject(basic, "data_send_port", json_config.data_send_port);
    cJSON_AddItemToObject(json_data, "basic", basic);
    
    // 会议配置
    cJSON *conference = cJSON_CreateObject();
    cJSON_AddNumberToObject(conference, "get_cfg_method", json_config.get_cfg_method);
    cJSON_AddNumberToObject(conference, "network_mode", json_config.network_mode);
    cJSON_AddNumberToObject(conference, "vbus_base_port", json_config.vbus_base_port);
    cJSON_AddNumberToObject(conference, "net_address", json_config.net_address);
    cJSON_AddNumberToObject(conference, "base_type", json_config.base_type);
    cJSON_AddNumberToObject(conference, "vcan_number", json_config.vcan_number);
    cJSON_AddNumberToObject(conference, "buffertime", json_config.buffertime);
    cJSON_AddNumberToObject(conference, "downtime", json_config.downtime);
    cJSON_AddNumberToObject(conference, "resettime", json_config.resettime);
    cJSON_AddNumberToObject(conference, "peer_base_num", json_config.peer_base_num);
    cJSON_AddNumberToObject(conference, "spec_function", json_config.spec_function);
    cJSON_AddItemToObject(json_data, "conference", conference);
    
    // 设备类型
    cJSON_AddNumberToObject(json_data, "device_type", json_config.device_type);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理录音基站配置保存请求
 * POST /api/v1/config/device/recorder
 */
int recorder_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    recorder_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从URL中提取设备类型（如果有）
    recorder_device_type_t device_type = RECORDER_DEVICE_TYPE_RECORDER;
    if (strstr(url, "mini")) {
        device_type = RECORDER_DEVICE_TYPE_MINI;
    }
    json_config.device_type = device_type;
    
    // 解析网络配置
    cJSON *network = cJSON_GetObjectItem(json, "network");
    if (network) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(network, "ip"))) {
            strncpy(json_config.ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "mask"))) {
            strncpy(json_config.mask, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "gateway"))) {
            strncpy(json_config.gateway, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "dns"))) {
            strncpy(json_config.dns, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "mac"))) {
            strncpy(json_config.mac, item->valuestring, LEN_MAC_ADDR-1);
        }
    }
    
    // 解析基础配置
    cJSON *basic = cJSON_GetObjectItem(json, "basic");
    if (basic) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(basic, "daemon_ip"))) {
            strncpy(json_config.daemon_ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(basic, "daemon_port"))) {
            json_config.daemon_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_ip"))) {
            strncpy(json_config.log_ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(basic, "log_port"))) {
            json_config.log_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_level"))) {
            json_config.log_level = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_to_where"))) {
            json_config.log_to_where = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "data_listen_port"))) {
            json_config.data_listen_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "data_send_port"))) {
            json_config.data_send_port = (uint16_t)item->valuedouble;
        }
    }
    
    // 解析会议配置
    cJSON *conference = cJSON_GetObjectItem(json, "conference");
    if (conference) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(conference, "get_cfg_method"))) {
            json_config.get_cfg_method = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "network_mode"))) {
            json_config.network_mode = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "vbus_base_port"))) {
            json_config.vbus_base_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "net_address"))) {
            json_config.net_address = (uint32_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "base_type"))) {
            json_config.base_type = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "vcan_number"))) {
            json_config.vcan_number = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "buffertime"))) {
            json_config.buffertime = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "downtime"))) {
            json_config.downtime = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "resettime"))) {
            json_config.resettime = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "peer_base_num"))) {
            json_config.peer_base_num = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "spec_function"))) {
            json_config.spec_function = (uint16_t)item->valuedouble;
        }
    }
    
    // 解析设备类型（如果提供）
    cJSON *device_type_item = cJSON_GetObjectItem(json, "device_type");
    if (device_type_item) {
        json_config.device_type = (recorder_device_type_t)device_type_item->valuedouble;
    }
    
    cJSON_Delete(json);
    
    // 验证配置数据
    if (validate_recorder_config(&json_config) != 0) {
        return response_send_error(connection, 400, "Invalid configuration data");
    }
    
    // 转换为二进制格式
    recorder_config_binary_t binary_config;
    if (convert_recorder_json_to_binary(&json_config, &binary_config) != 0) {
        return response_send_error(connection, 500, "Configuration conversion failed");
    }
    
    // 写入配置文件
    if (write_recorder_base_config(&binary_config, json_config.device_type) < 0) {
        return response_send_error(connection, 500, "Failed to save configuration");
    }
    
    // 创建成功响应
    cJSON *response_json = cJSON_CreateObject();
    cJSON_AddStringToObject(response_json, "status", "success");
    cJSON_AddStringToObject(response_json, "message", "Recorder configuration saved successfully");
    
    int result = response_send_success(connection, response_json);
    cJSON_Delete(response_json);
    
    return result;
}
