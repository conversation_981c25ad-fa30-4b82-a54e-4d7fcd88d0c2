#ifndef CONFIG_RECORDER_RECORDER_CONFIG_H
#define CONFIG_RECORDER_RECORDER_CONFIG_H

#include "config/common/config_base.h"
#include <stdint.h>
#include <cJSON.h>

/**
 * @file recorder_config.h
 * @brief 录音基站配置模块
 * <AUTHOR> Assistant
 * @date 2025-01-01
 * 
 * 基于deprecated/cgi/0mini.c和0recorder.c，确保100%兼容
 * 录音模块和最小基站模块使用相同的数据结构，仅设备类型不同
 */

// 设备类型枚举 - 基于旧项目0define.h
typedef enum {
    RECORDER_DEVICE_TYPE_RECORDER = 0x13,    // 录音模块 (eTypeRecorder)
    RECORDER_DEVICE_TYPE_MINI = 0x17         // 最小基站 (eTypeMini)
} recorder_device_type_t;

// 配置文件路径定义（与原始保持一致）
#define RECORDERCFG   "/home/<USER>/cfg/recorder.cfg"
#define START_ADDR_RECORDER  0x00

// 长度定义 - 基于deprecated/cgi/inc/1utils.h
#define LEN_IP_ADDR         16
#define LEN_MAC_ADDR        18
#define LEN_NAME            30
#define LEN_PASS            20

// 错误码定义 - 基于deprecated/cgi/inc/0define.h
#define RECORDER_READ_SUCCESS    0x0000
#define RECORDER_OPEN_ETH_ERR    0x0001
#define RECORDER_OPEN_COMMON_ERR 0x0002
#define RECORDER_OPEN_BASIC_ERR  0x0004
#define RECORDER_OPEN_CONF_ERR   0x0008
#define RECORDER_SAVE_SUCCESS    0x8000

/**
 * 网络配置结构体 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    char ip[LEN_IP_ADDR];          // IP地址字符串
    char mask[LEN_IP_ADDR];        // 子网掩码
    char gateway[LEN_IP_ADDR];     // 网关地址
    char dns[LEN_IP_ADDR];         // DNS服务器
    char mac[LEN_MAC_ADDR];        // MAC地址
} recorder_net_config_t;

/**
 * 板卡网络配置 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    FILE *fpeth;
    recorder_net_config_t ethconfig;
    int wlan_enable_val;
} recorder_board_net_t;

/**
 * 数字化网络配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t ip;          // IP地址（网络字节序）
    uint32_t mask;        // 子网掩码
    uint32_t gateway;     // 网关
    uint32_t dns;         // DNS服务器
    uint8_t mac[6];       // MAC地址（6字节）
} __attribute__((packed)) recorder_cfg_net_t;

/**
 * 通用配置结构体 - 基于deprecated/cgi/inc/1rwcommon.h
 */
typedef struct {
    uint8_t ds;           // 调度
    uint8_t sw;           // 交换机
    uint8_t conf_num;     // 会议数
    uint8_t normal_num;   // 常态数
} __attribute__((packed)) recorder_cfg_common_t;

/**
 * 板卡基础配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t daemon_ip;
    uint16_t daemon_port;
    uint32_t log_ip;
    uint16_t log_port;
    uint32_t cfg_ip;
    uint16_t cfg_port;
    uint8_t log_level;
    uint8_t log_to_where;
    uint16_t data_listen_port;
    uint16_t data_send_port;
} __attribute__((packed)) recorder_cfg_board_basic_t;

/**
 * 会议配置结构体 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint8_t get_cfg_method;
    uint8_t network_mode;
    uint32_t voice_ip;
    uint16_t data_listen_port;
    uint16_t vbus_base_port;
    uint32_t net_address:24;
    uint8_t base_type;
    uint8_t vbus_to_chan[12];
    uint8_t vcan_number;
    uint16_t buffertime;
    uint16_t downtime;
    uint8_t resettime;
    uint8_t peer_base_num;
    uint16_t spec_function;
} __attribute__((packed)) recorder_cfg_conf_t;

/**
 * 对端基站配置 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint32_t peer_ip;
    uint32_t peer_voice_ip;
    uint16_t peer_data_listen_port;
    uint16_t peer_voice_port_base;
    uint32_t peer_net_address:24;
    uint8_t peer_type;
    uint8_t peer_vbus_to_chan[12];
} __attribute__((packed)) recorder_cfg_peer_base_t;

/**
 * 录音基站配置二进制结构体（与deprecated/cgi 100%兼容）
 */
typedef struct {
    recorder_board_net_t board_net;              // 网络板卡配置
    recorder_cfg_net_t net_config;               // 数字化网络配置
    recorder_cfg_common_t common_config;         // 通用配置
    recorder_cfg_board_basic_t board_basic;      // 板卡基础配置
    recorder_cfg_conf_t conf_config;             // 会议配置
    recorder_cfg_peer_base_t peer_config;        // 对端基站配置
    recorder_device_type_t device_type;          // 设备类型
} recorder_config_binary_t;

/**
 * 录音基站配置JSON结构（API交互）
 */
typedef struct {
    // 网络配置
    char ip[LEN_IP_ADDR];
    char mask[LEN_IP_ADDR];
    char gateway[LEN_IP_ADDR];
    char dns[LEN_IP_ADDR];
    char mac[LEN_MAC_ADDR];
    
    // 基础配置
    char daemon_ip[LEN_IP_ADDR];
    uint16_t daemon_port;
    char log_ip[LEN_IP_ADDR];
    uint16_t log_port;
    uint8_t log_level;
    uint8_t log_to_where;
    uint16_t data_listen_port;
    uint16_t data_send_port;
    
    // 会议配置
    uint8_t get_cfg_method;
    uint8_t network_mode;
    uint16_t vbus_base_port;
    uint32_t net_address;
    uint8_t base_type;
    uint8_t vcan_number;
    uint16_t buffertime;
    uint16_t downtime;
    uint8_t resettime;
    uint8_t peer_base_num;
    uint16_t spec_function;
    
    // 设备类型
    recorder_device_type_t device_type;
} recorder_config_json_t;

// 配置操作接口
extern const config_operations_t recorder_config_ops;

// 核心配置操作函数
int recorder_config_read(recorder_config_binary_t *config);
int recorder_config_write(const recorder_config_binary_t *config);
int recorder_config_validate(const recorder_config_json_t *config);
int recorder_config_json_to_binary(const recorder_config_json_t *json, recorder_config_binary_t *binary);
int recorder_config_binary_to_json(const recorder_config_binary_t *binary, recorder_config_json_t *json);
void recorder_config_set_default(recorder_config_binary_t *config);

// 兼容性函数（保持与原有device_config.c的接口一致）
int validate_recorder_config(const recorder_config_json_t *config);
int convert_recorder_json_to_binary(const recorder_config_json_t *json_config, recorder_config_binary_t *binary_config);
int convert_recorder_binary_to_json(const recorder_config_binary_t *binary_config, recorder_config_json_t *json_config);
int read_recorder_config_file(recorder_config_binary_t *config);
int write_recorder_config_file(const recorder_config_binary_t *config);
void set_default_recorder_config(recorder_config_binary_t *config);

// 设备类型相关函数
int read_recorder_base_config(recorder_config_binary_t *config, recorder_device_type_t device_type);
int write_recorder_base_config(const recorder_config_binary_t *config, recorder_device_type_t device_type);

// 工具函数
void recorder_ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size);
uint32_t recorder_ip_str_to_num(const char *ip_str);
void eth_str_to_num(const recorder_net_config_t *pConfig, recorder_cfg_net_t *pNet);
void eth_num_to_str(const recorder_cfg_net_t *pNet, recorder_net_config_t *pConfig);

// API函数声明
struct MHD_Connection; // 前向声明

/**
 * 处理录音基站配置获取请求
 * GET /api/v1/config/device/recorder
 */
int recorder_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data);

/**
 * 处理录音基站配置保存请求
 * POST /api/v1/config/device/recorder
 */
int recorder_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data);


#endif /* CONFIG_RECORDER_RECORDER_CONFIG_H */
