#include "config/device_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

// 移除了detect_board_type函数，因为旧项目中不存在此功能
// 保持与旧项目100%功能一致，不添加额外的硬件检测功能

/**
 * 获取板卡类型字符串
 */
const char* get_board_type_string(board_type_t board_type) {
    switch (board_type) {
        case BOARD_TYPE_IP_SWITCH:
            return "IP交换机板卡";
        case BOARD_TYPE_RECORDER:
            return "录音板卡";
        case BOARD_TYPE_GATEWAY:
            return "网关板卡";
        case BOARD_TYPE_STATION:
            return "基站板卡";
        default:
            return "未知板卡";
    }
}

/**
 * 获取录音设备类型名称
 */
const char* get_recorder_base_device_name(recorder_base_device_type_t device_type) {
    switch (device_type) {
        case RECORDER_DEVICE_TYPE_RECORDER:
            return "录音模块";
        case RECORDER_DEVICE_TYPE_MINI:
            return "最小基站";
        default:
            return "未知设备";
    }
}
