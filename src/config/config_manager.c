/*******************************************************************
 * File          : config_manager.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 配置管理器实现 - 配置工厂模式
 * 统一管理所有配置模块的创建、读取、写入和验证
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : 实现配置工厂模式，统一配置管理
 *******************************************************************/

#include "config/config_manager.h"
#include "config/network_config.h"
#include "config/center/center_config.h"
#include "config/gateway/gateway_config.h"
#include "config/recorder/recorder_config.h"
#include "config/station/station_config.h"
#include "config/switch/switch_config.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// 配置映射表
static config_mapping_t g_config_mappings[CONFIG_TYPE_MAX];
static int g_manager_initialized = 0;

// ===================== 配置验证函数包装器 =====================

static int network_config_validator(const cJSON *data) {
    // TODO: 实现网络配置验证
    (void)data;
    return 0;
}

static int center_config_validator(const cJSON *data) {
    center_config_json_t config;
    memset(&config, 0, sizeof(config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(data, "ip"))) {
        strncpy(config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return validate_center_config(&config);
}

static int gateway_config_validator(const cJSON *data) {
    gateway_config_json_t config;
    memset(&config, 0, sizeof(config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(data, "ip"))) {
        strncpy(config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return validate_gateway_config(&config);
}

static int recorder_config_validator(const cJSON *data) {
    recorder_config_json_t config;
    memset(&config, 0, sizeof(config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(data, "ip"))) {
        strncpy(config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return validate_recorder_config(&config);
}

static int station_config_validator(const cJSON *data) {
    // TODO: 实现基站配置验证
    (void)data;
    return 0;
}

static int switch_config_validator(const cJSON *data) {
    // TODO: 实现交换机配置验证
    (void)data;
    return 0;
}

// ===================== 配置转换函数包装器 =====================

static int network_json_to_binary(const cJSON *json_data, void *binary_data) {
    // TODO: 实现网络配置JSON到二进制转换
    (void)json_data;
    (void)binary_data;
    return 0;
}

static int center_json_to_binary(const cJSON *json_data, void *binary_data) {
    center_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(json_data, "ip"))) {
        strncpy(json_config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return convert_center_json_to_binary(&json_config, (center_config_binary_t*)binary_data);
}

static int gateway_json_to_binary(const cJSON *json_data, void *binary_data) {
    gateway_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(json_data, "ip"))) {
        strncpy(json_config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return convert_gateway_json_to_binary(&json_config, (gateway_config_binary_t*)binary_data);
}

static int recorder_json_to_binary(const cJSON *json_data, void *binary_data) {
    recorder_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));

    // 从JSON解析到结构体
    cJSON *item;
    if ((item = cJSON_GetObjectItem(json_data, "ip"))) {
        strncpy(json_config.ip, item->valuestring, LEN_IP_ADDR-1);
    }
    // ... 其他字段解析

    return convert_recorder_json_to_binary(&json_config, (recorder_config_binary_t*)binary_data);
}

static int center_binary_to_json(const void *binary_data, cJSON *json_data) {
    center_config_json_t json_config;
    int ret = convert_center_binary_to_json((const center_config_binary_t*)binary_data, &json_config);
    if (ret != 0) {
        return ret;
    }

    // 将结构体转换为JSON
    cJSON_AddStringToObject(json_data, "ip", json_config.ip);
    // ... 其他字段添加

    return 0;
}

static int gateway_binary_to_json(const void *binary_data, cJSON *json_data) {
    gateway_config_json_t json_config;
    int ret = convert_gateway_binary_to_json((const gateway_config_binary_t*)binary_data, &json_config);
    if (ret != 0) {
        return ret;
    }

    // 将结构体转换为JSON
    cJSON_AddStringToObject(json_data, "ip", json_config.ip);
    // ... 其他字段添加

    return 0;
}

static int recorder_binary_to_json(const void *binary_data, cJSON *json_data) {
    recorder_config_json_t json_config;
    int ret = convert_recorder_binary_to_json((const recorder_config_binary_t*)binary_data, &json_config);
    if (ret != 0) {
        return ret;
    }

    // 将结构体转换为JSON
    cJSON_AddStringToObject(json_data, "ip", json_config.ip);
    // ... 其他字段添加

    return 0;
}

// ===================== 配置管理器主要函数 =====================

/**
 * 初始化配置管理器
 */
int config_manager_init(void) {
    if (g_manager_initialized) {
        return 0; // 已经初始化
    }

    // 清空配置映射表
    memset(g_config_mappings, 0, sizeof(g_config_mappings));

    // 注册网络配置
    g_config_mappings[CONFIG_TYPE_NETWORK] = (config_mapping_t){
        .type = CONFIG_TYPE_NETWORK,
        .name = "network",
        .file_path = "/home/<USER>/cfg/network.cfg",
        .data_size = sizeof(network_config_binary_t),
        .validator = network_config_validator,
        .json_to_binary = network_json_to_binary,
        .binary_to_json = NULL // TODO: 实现
    };

    // 注册呼叫中心配置
    g_config_mappings[CONFIG_TYPE_CENTER] = (config_mapping_t){
        .type = CONFIG_TYPE_CENTER,
        .name = "center",
        .file_path = "/home/<USER>/cfg/center.cfg",
        .data_size = sizeof(center_config_binary_t),
        .validator = center_config_validator,
        .json_to_binary = center_json_to_binary,
        .binary_to_json = center_binary_to_json
    };

    // 注册网关配置
    g_config_mappings[CONFIG_TYPE_GATEWAY] = (config_mapping_t){
        .type = CONFIG_TYPE_GATEWAY,
        .name = "gateway",
        .file_path = "/home/<USER>/cfg/gateway.cfg",
        .data_size = sizeof(gateway_config_binary_t),
        .validator = gateway_config_validator,
        .json_to_binary = gateway_json_to_binary,
        .binary_to_json = gateway_binary_to_json
    };

    // 注册录音基站配置
    g_config_mappings[CONFIG_TYPE_RECORDER] = (config_mapping_t){
        .type = CONFIG_TYPE_RECORDER,
        .name = "recorder",
        .file_path = "/home/<USER>/cfg/recorder.cfg",
        .data_size = sizeof(recorder_config_binary_t),
        .validator = recorder_config_validator,
        .json_to_binary = recorder_json_to_binary,
        .binary_to_json = recorder_binary_to_json
    };

    // 注册基站配置
    g_config_mappings[CONFIG_TYPE_SCI] = (config_mapping_t){
        .type = CONFIG_TYPE_SCI,
        .name = "station",
        .file_path = "/home/<USER>/cfg/station.cfg",
        .data_size = sizeof(station_config_binary_t),
        .validator = station_config_validator,
        .json_to_binary = NULL, // TODO: 实现
        .binary_to_json = NULL  // TODO: 实现
    };

    // 注册交换机配置
    g_config_mappings[CONFIG_TYPE_SWITCH] = (config_mapping_t){
        .type = CONFIG_TYPE_SWITCH,
        .name = "switch",
        .file_path = "/home/<USER>/cfg/switch.cfg",
        .data_size = sizeof(switch_config_binary_t),
        .validator = switch_config_validator,
        .json_to_binary = NULL, // TODO: 实现
        .binary_to_json = NULL  // TODO: 实现
    };

    g_manager_initialized = 1;
    return 0;
}

/**
 * 销毁配置管理器
 */
void config_manager_destroy(void) {
    if (!g_manager_initialized) {
        return;
    }

    // 清理资源
    memset(g_config_mappings, 0, sizeof(g_config_mappings));
    g_manager_initialized = 0;
}

/**
 * 注册配置映射
 */
int config_manager_register(const config_mapping_t *mapping) {
    if (!g_manager_initialized || !mapping) {
        return -1;
    }

    if (mapping->type >= CONFIG_TYPE_MAX) {
        return -1;
    }

    g_config_mappings[mapping->type] = *mapping;
    return 0;
}

/**
 * 获取配置映射
 */
const config_mapping_t* config_manager_get_mapping(config_type_t type) {
    if (!g_manager_initialized || type >= CONFIG_TYPE_MAX) {
        return NULL;
    }

    return &g_config_mappings[type];
}

/**
 * 读取配置数据
 */
int config_manager_read(config_type_t type, cJSON **json_data) {
    if (!g_manager_initialized || !json_data || type >= CONFIG_TYPE_MAX) {
        return -1;
    }

    const config_mapping_t *mapping = &g_config_mappings[type];
    if (!mapping->file_path || !mapping->binary_to_json) {
        return -1; // 配置未注册或转换函数未实现
    }

    // 分配二进制数据缓冲区
    void *binary_data = malloc(mapping->data_size);
    if (!binary_data) {
        return -1;
    }

    int ret = -1;

    // 根据配置类型调用相应的读取函数
    switch (type) {
        case CONFIG_TYPE_CENTER:
            ret = read_center_config_file((center_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_GATEWAY:
            ret = read_gateway_config_file((gateway_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_RECORDER:
            ret = read_recorder_config_file((recorder_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_NETWORK:
            ret = read_network_config_file((network_config_binary_t*)binary_data);
            break;
        default:
            ret = -1; // 未实现的配置类型
            break;
    }

    if (ret == 0) {
        // 创建JSON对象
        *json_data = cJSON_CreateObject();
        if (*json_data) {
            ret = mapping->binary_to_json(binary_data, *json_data);
            if (ret != 0) {
                cJSON_Delete(*json_data);
                *json_data = NULL;
            }
        } else {
            ret = -1;
        }
    }

    free(binary_data);
    return ret;
}

/**
 * 保存配置数据
 */
int config_manager_save(config_type_t type, const cJSON *json_data) {
    if (!g_manager_initialized || !json_data || type >= CONFIG_TYPE_MAX) {
        return -1;
    }

    const config_mapping_t *mapping = &g_config_mappings[type];
    if (!mapping->file_path || !mapping->json_to_binary) {
        return -1; // 配置未注册或转换函数未实现
    }

    // 分配二进制数据缓冲区
    void *binary_data = malloc(mapping->data_size);
    if (!binary_data) {
        return -1;
    }

    // 转换JSON到二进制格式
    int ret = mapping->json_to_binary(json_data, binary_data);
    if (ret != 0) {
        free(binary_data);
        return ret;
    }

    // 根据配置类型调用相应的写入函数
    switch (type) {
        case CONFIG_TYPE_CENTER:
            ret = write_center_config_file((const center_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_GATEWAY:
            ret = write_gateway_config_file((const gateway_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_RECORDER:
            ret = write_recorder_config_file((const recorder_config_binary_t*)binary_data);
            break;
        case CONFIG_TYPE_NETWORK:
            ret = write_network_config_file((const network_config_binary_t*)binary_data);
            break;
        default:
            ret = -1; // 未实现的配置类型
            break;
    }

    free(binary_data);
    return ret;
}

/**
 * 验证配置数据
 */
int config_manager_validate(config_type_t type, const cJSON *json_data) {
    if (!g_manager_initialized || !json_data || type >= CONFIG_TYPE_MAX) {
        return -1;
    }

    const config_mapping_t *mapping = &g_config_mappings[type];
    if (!mapping->validator) {
        return 0; // 没有验证函数，认为有效
    }

    return mapping->validator(json_data);
}