#include "config/network_config.h"
#include "utils/validation/ip_validator.h"
#include "utils/validation/mac_validator.h"
#include "utils/conversion/mac_converter.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <regex.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

// IP和MAC地址验证函数现在从utils模块导入

/**
 * 验证网络配置数据
 */
int validate_network_config(const network_config_json_t *config) {
    if (!config) return -1;
    
    // 验证IP地址
    if (!validate_ip_address(config->ip)) {
        fprintf(stderr, "Invalid IP address: %s\n", config->ip);
        return -1;
    }
    
    // 验证子网掩码
    if (!validate_ip_address(config->mask)) {
        fprintf(stderr, "Invalid subnet mask: %s\n", config->mask);
        return -1;
    }
    
    // 验证网关地址
    if (!validate_ip_address(config->gateway)) {
        fprintf(stderr, "Invalid gateway address: %s\n", config->gateway);
        return -1;
    }
    
    // 验证DNS服务器
    if (!validate_ip_address(config->dns)) {
        fprintf(stderr, "Invalid DNS server: %s\n", config->dns);
        return -1;
    }
    
    // 验证MAC地址
    if (!validate_mac_address(config->mac)) {
        fprintf(stderr, "Invalid MAC address: %s\n", config->mac);
        return -1;
    }
    
    return 0;
}

// MAC地址转换函数现在从utils模块导入

/**
 * JSON转换为二进制配置
 */
int convert_network_json_to_binary(const cJSON *json_data, network_config_binary_t *binary_config) {
    if (!json_data || !binary_config) return -1;
    
    cJSON *item;
    network_config_json_t json_config;
    
    // 初始化结构体
    memset(&json_config, 0, sizeof(json_config));
    
    // 提取JSON字段
    item = cJSON_GetObjectItemCaseSensitive(json_data, "ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.ip, item->valuestring, sizeof(json_config.ip) - 1);
    } else {
        fprintf(stderr, "Missing or invalid 'ip' field\n");
        return -1;
    }
    
    item = cJSON_GetObjectItemCaseSensitive(json_data, "mask");
    if (cJSON_IsString(item)) {
        strncpy(json_config.mask, item->valuestring, sizeof(json_config.mask) - 1);
    } else {
        fprintf(stderr, "Missing or invalid 'mask' field\n");
        return -1;
    }
    
    item = cJSON_GetObjectItemCaseSensitive(json_data, "gateway");
    if (cJSON_IsString(item)) {
        strncpy(json_config.gateway, item->valuestring, sizeof(json_config.gateway) - 1);
    } else {
        fprintf(stderr, "Missing or invalid 'gateway' field\n");
        return -1;
    }
    
    item = cJSON_GetObjectItemCaseSensitive(json_data, "dns");
    if (cJSON_IsString(item)) {
        strncpy(json_config.dns, item->valuestring, sizeof(json_config.dns) - 1);
    } else {
        fprintf(stderr, "Missing or invalid 'dns' field\n");
        return -1;
    }
    
    item = cJSON_GetObjectItemCaseSensitive(json_data, "mac");
    if (cJSON_IsString(item)) {
        strncpy(json_config.mac, item->valuestring, sizeof(json_config.mac) - 1);
    } else {
        fprintf(stderr, "Missing or invalid 'mac' field\n");
        return -1;
    }
    
    // 验证数据
    if (validate_network_config(&json_config) != 0) {
        return -1;
    }
    
    // 转换为二进制格式
    binary_config->ip = inet_addr(json_config.ip);
    binary_config->mask = inet_addr(json_config.mask);
    binary_config->gateway = inet_addr(json_config.gateway);
    binary_config->dns = inet_addr(json_config.dns);
    
    if (mac_str_to_bytes(json_config.mac, binary_config->mac) != 0) {
        fprintf(stderr, "Failed to convert MAC address\n");
        return -1;
    }
    
    return 0;
}

/**
 * 二进制配置转换为JSON
 */
int convert_network_binary_to_json(const network_config_binary_t *binary_config, cJSON **json_data) {
    if (!binary_config || !json_data) return -1;
    
    cJSON *json = cJSON_CreateObject();
    if (!json) return -1;
    
    // IP地址转换
    struct in_addr addr;
    addr.s_addr = binary_config->ip;
    cJSON_AddStringToObject(json, "ip", inet_ntoa(addr));
    
    // 子网掩码转换
    addr.s_addr = binary_config->mask;
    cJSON_AddStringToObject(json, "mask", inet_ntoa(addr));
    
    // 网关地址转换
    addr.s_addr = binary_config->gateway;
    cJSON_AddStringToObject(json, "gateway", inet_ntoa(addr));
    
    // DNS服务器转换
    addr.s_addr = binary_config->dns;
    cJSON_AddStringToObject(json, "dns", inet_ntoa(addr));
    
    // MAC地址转换
    char mac_str[18];
    if (mac_bytes_to_str(binary_config->mac, mac_str, sizeof(mac_str)) == 0) {
        cJSON_AddStringToObject(json, "mac", mac_str);
    } else {
        cJSON_Delete(json);
        return -1;
    }
    
    *json_data = json;
    return 0;
}

/**
 * 读取网络配置文件（兼容原有二进制格式）
 */
int read_network_config_file(network_config_binary_t *config) {
    if (!config) return -1;
    
    FILE *fp = fopen(NETWORK_CONFIG_FILE, "rb");
    if (!fp) {
        perror("Failed to open network config file for reading");
        return -1;
    }
    
    size_t read_size = fread(config, 1, sizeof(network_config_binary_t), fp);
    fclose(fp);
    
    if (read_size != sizeof(network_config_binary_t)) {
        fprintf(stderr, "Failed to read complete network config\n");
        return -1;
    }
    
    return 0;
}

/**
 * 写入网络配置文件（保持原有二进制格式）
 */
int write_network_config_file(const network_config_binary_t *config) {
    if (!config) return -1;
    
    FILE *fp = fopen(NETWORK_CONFIG_FILE, "wb");
    if (!fp) {
        perror("Failed to open network config file for writing");
        return -1;
    }
    
    size_t write_size = fwrite(config, 1, sizeof(network_config_binary_t), fp);
    fclose(fp);
    
    if (write_size != sizeof(network_config_binary_t)) {
        fprintf(stderr, "Failed to write complete network config\n");
        return -1;
    }
    
    // 确保文件权限
    chmod(NETWORK_CONFIG_FILE, 0644);
    
    return 0;
}

// 占位符实现 - 第二阶段将完整实现

// TODO: 实现网络配置处理功能 