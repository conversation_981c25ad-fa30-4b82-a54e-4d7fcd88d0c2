#include "station_config.h"
#include "utils/validation/ip_validator.h"
#include "utils/validation/common_validator.h"
#include "utils/conversion/data_converter.h"
#include "utils/file/file_utils.h"
#include "utils/file/config_file_manager.h"
#include "utils/network_utils.h"
#include "utils/system_utils.h"
#include "config/defaults.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

/**
 * @file station_config.c
 * @brief 基站配置模块实现
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0sci*.c的业务逻辑，确保100%兼容
 */

// ==================== 核心配置操作函数 ====================

/**
 * 读取基站配置
 */
int station_config_read(station_config_binary_t *config) {
    return read_station_config_file(config);
}

/**
 * 写入基站配置
 */
int station_config_write(const station_config_binary_t *config) {
    return write_station_config_file(config);
}

/**
 * 验证基站配置（基于JSON格式）
 */
int station_config_validate(const station_config_json_t *config) {
    return validate_station_config(config);
}

/**
 * JSON转二进制配置
 */
int station_config_json_to_binary(const station_config_json_t *json, station_config_binary_t *binary) {
    return convert_station_json_to_binary(json, binary);
}

/**
 * 二进制转JSON配置
 */
int station_config_binary_to_json(const station_config_binary_t *binary, station_config_json_t *json) {
    return convert_station_binary_to_json(binary, json);
}

/**
 * 设置默认配置
 */
void station_config_set_default(station_config_binary_t *config) {
    // 使用默认的基站类型，不进行硬件检测（保持与旧项目一致）
    set_default_station_config(config, STATION_TYPE_BASE);
}

// ==================== 兼容性函数实现 ====================


/**
 * 验证基站配置数据
 * 保持与原有device_config.c中实现完全一致
 */
int validate_station_config(const station_config_json_t *config) {
    if (!config) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    
    // 验证网络模式
    if (config->network_mode > 2) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证IP地址格式 - 使用统一验证接口
    if (ip_validate_address(config->voice_ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    
    // 验证端口范围 - 使用统一验证接口
    if (validate_data_port(config->data_listen_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    if (validate_voice_port(config->vbus_base_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    // 验证网络地址（24位有效）- 使用统一验证
    if (validate_24bit_id(config->net_address) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证基站类型 - 使用统一验证
    if (!IS_VALID_DEVICE_TYPE(config->base_type)) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证VCAN数量 - 使用统一常量
    if (config->vcan_number > VCAN_MAX) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证缓冲时间和掉线时间 - 使用统一验证
    if (validate_buffer_time(config->buffertime) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    if (validate_voice_down_time(config->downtime) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    return VALIDATION_SUCCESS;
}

/**
 * JSON格式转换为二进制配置
 * 保持与原有device_config.c中实现完全一致
 */
int convert_station_json_to_binary(const station_config_json_t *json_config, 
                                  station_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(station_config_binary_t));
    
    // 转换字段 - 使用安全接口
    binary_config->get_cfg_method = json_config->get_cfg_method;
    binary_config->network_mode = json_config->network_mode;
    uint32_t ip_num = ip_str_to_uint32_safe(json_config->voice_ip);
    if (ip_num == INADDR_NONE) {
        return CONVERTER_ERROR_FORMAT;
    }
    binary_config->voice_ip = ip_num;
    binary_config->data_listen_port = json_config->data_listen_port;
    binary_config->vbus_base_port = json_config->vbus_base_port;
    binary_config->net_address = data_get_24bit_value(json_config->net_address);  // 使用统一24位处理
    binary_config->base_type = json_config->base_type;
    binary_config->vcan_number = json_config->vcan_number;
    binary_config->buffertime = json_config->buffertime;
    binary_config->downtime = json_config->downtime;
    binary_config->resettime = json_config->resettime;
    
    // 复制语音总线映射数组
    memcpy(binary_config->vbus_to_chan, json_config->vbus_to_chan, 
           sizeof(binary_config->vbus_to_chan));
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 * 保持与原有device_config.c中实现完全一致
 */
int convert_station_binary_to_json(const station_config_binary_t *binary_config, 
                                  station_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(station_config_json_t));
    
    // 转换字段 - 使用安全接口
    json_config->get_cfg_method = binary_config->get_cfg_method;
    json_config->network_mode = binary_config->network_mode;
    if (ip_uint32_to_str_safe(binary_config->voice_ip, json_config->voice_ip, sizeof(json_config->voice_ip)) != CONVERTER_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    json_config->data_listen_port = binary_config->data_listen_port;
    json_config->vbus_base_port = binary_config->vbus_base_port;
    json_config->net_address = binary_config->net_address;
    json_config->base_type = binary_config->base_type;
    json_config->vcan_number = binary_config->vcan_number;
    json_config->buffertime = binary_config->buffertime;
    json_config->downtime = binary_config->downtime;
    json_config->resettime = binary_config->resettime;
    
    // 复制语音总线映射数组
    memcpy(json_config->vbus_to_chan, binary_config->vbus_to_chan, 
           sizeof(json_config->vbus_to_chan));
    
    // 设置扩展字段默认值
    json_config->station_id = json_config->net_address;  // 使用网络地址作为基站ID
    snprintf(json_config->station_name, sizeof(json_config->station_name), 
             "Station_%06X", json_config->net_address);
    json_config->signal_strength = 5;  // 默认信号强度
    json_config->auto_register = 1;    // 默认自动注册
    
    return 0;
}


/**
 * 设置基站配置默认值
 * 基于原有device_config.c的实现
 */
void set_default_station_config(station_config_binary_t *config, station_type_t station_type) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(station_config_binary_t));
    
    // 设置通用默认值 - 使用统一默认值
    config->get_cfg_method = 0;                          // 默认获取方法
    config->network_mode = 0;                            // 默认网络模式
    config->voice_ip = ip_str_to_uint32_safe(DEFAULT_VOICE_IP); // 使用安全IP转换
    config->data_listen_port = DEFAULT_DATA_LISTEN_PORT; // 默认数据监听端口
    config->vbus_base_port = get_default_voice_port();   // 使用统一默认语音端口
    config->net_address = DEFAULT_NET_ADDRESS;           // 默认网络地址
    config->base_type = station_type;                    // 设备类型
    config->vcan_number = 1;                             // 默认VCAN数量
    config->buffertime = get_default_buffer_time();      // 使用统一默认缓冲时间
    config->downtime = get_default_voice_down_time();    // 使用统一默认掉线时间
    config->resettime = 10;                              // 默认重置时间（10秒）
    
    // 设置默认语音总线映射
    for (int i = 0; i < 12; i++) {
        config->vbus_to_chan[i] = i;
    }
    
    // 根据设备类型调整特定参数
    switch (station_type) {
        case STATION_TYPE_3G:
            config->data_listen_port = DEFAULT_DATA_LISTEN_PORT + 100;
            config->vbus_base_port = DEFAULT_VBUS_BASE_PORT + 100;
            break;
        case STATION_TYPE_SCI:
            config->data_listen_port = DEFAULT_DATA_LISTEN_PORT;
            config->vbus_base_port = DEFAULT_VBUS_BASE_PORT;
            break;
        default:
            // 使用通用默认值
            break;
    }
}

/**
 * 获取基站类型字符串
 * 保持与原有device_config.c中实现完全一致
 */
const char* get_station_type_string(station_type_t station_type) {
    switch (station_type) {
        case STATION_TYPE_BASE:
            return "Base Station";
        case STATION_TYPE_3G:
            return "3G Station";
        case STATION_TYPE_SCI:
            return "SCI Station";
        default:
            return "Unknown";
    }
}

// ==================== 对端配置函数 ====================

/**
 * 读取对端配置
 */
int station_peer_config_read(station_peer_config_binary_t *config, int peer_index) {
    if (!config || peer_index < 0 || peer_index >= 8) {
        return -1;
    }
    
    // 对端配置通常存储在会议配置文件中 - 使用统一文件接口
    const char *config_file = "/home/<USER>/cfg/conference.cfg"; // 修正文件名拼写
    
    // 计算对端配置在文件中的偏移位置
    size_t offset = sizeof(station_peer_config_binary_t) * peer_index;
    
    // 使用统一文件读取接口
    int result = file_read_at_offset(config_file, offset, config, sizeof(station_peer_config_binary_t));
    if (result != FILE_OPERATION_SUCCESS) {
        // 文件不存在或读取失败，设置默认值
        station_peer_config_set_default(config, STATION_TYPE_BASE);
        return 0;
    }
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入对端配置
 */
int station_peer_config_write(const station_peer_config_binary_t *config, int peer_index) {
    if (!config || peer_index < 0 || peer_index >= 8) {
        return -1;
    }
    
    // 对端配置通常存储在会议配置文件中
    FILE *fp = fopen("/home/<USER>/cfg/conferece.cfg", "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen("/home/<USER>/cfg/conferece.cfg", "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 计算对端配置在文件中的偏移位置
    size_t offset = sizeof(station_peer_config_binary_t) * peer_index;
    if (fseek(fp, offset, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入对端配置数据
    size_t write_size = fwrite(config, sizeof(station_peer_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置对端配置默认值
 */
void station_peer_config_set_default(station_peer_config_binary_t *config, station_type_t station_type) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(station_peer_config_binary_t));
    
    // 设置默认值
    config->peer_ip = ip_str_to_uint32_safe(DEFAULT_VOICE_IP);  // 使用安全IP转换
    config->peer_voice_ip = ip_str_to_uint32_safe(DEFAULT_VOICE_IP); // 使用安全IP转换
    config->peer_data_listen_port = DEFAULT_PEER_DATA_PORT;   // 默认对端数据端口
    config->peer_voice_port_base = DEFAULT_PEER_VOICE_PORT;   // 默认对端语音端口基址
    config->peer_net_address = DEFAULT_PEER_NET_ADDRESS;      // 默认对端网络地址
    config->peer_type = station_type;                   // 对端类型
    
    // 设置默认语音总线映射
    for (int i = 0; i < 12; i++) {
        config->peer_vbus_to_chan[i] = i;
    }
}

// ==================== 工具函数 ====================


// ==================== 统一配置接口实现 ====================

/**
 * 统一配置操作接口实例
 */
const config_operations_t station_config_ops = {
    .config_name = "station",
    .config_description = "基站配置",
    .json_config_size = sizeof(station_config_json_t),
    .binary_config_size = sizeof(station_config_binary_t),
    
    .validate_json = (config_validator_func_t)station_config_validate,
    .json_to_binary = (config_converter_func_t)station_config_json_to_binary,
    .binary_to_json = (config_converter_func_t)station_config_binary_to_json,
    .read_file = (config_file_func_t)station_config_read,
    .write_file = (config_file_write_func_t)station_config_write,
    .set_default = (config_default_func_t)station_config_set_default,
    
    .custom_data = NULL
}; 