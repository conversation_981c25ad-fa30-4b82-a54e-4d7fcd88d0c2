#ifndef CONFIG_STATION_STATION_CONFIG_H
#define CONFIG_STATION_STATION_CONFIG_H

#include "config/common/config_base.h"
#include <stdint.h>
#include <cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file station_config.h
 * @brief 基站配置模块
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0sci*.c文件，确保100%兼容
 */

// 配置文件路径定义（与原始保持一致）
#define SCICFG          "/home/<USER>/cfg/sci.cfg"
#define START_ADDR_SCI  0x00

// 设备类型枚举（基于deprecated/cgi/inc/0define.h）
typedef enum {
    STATION_TYPE_BASE = 0x11,    // 基站
    STATION_TYPE_3G = 0x18,      // 3G基站
    STATION_TYPE_SCI = 0x12,     // SCI基站（修正重复值）
    STATION_TYPE_UNKNOWN = 0x1F
} station_type_t;

// 基站配置二进制结构体（与deprecated/cgi/inc/1rwconference.h 100%兼容）
typedef struct {
    uint8_t get_cfg_method;         // 获取配置方法
    uint8_t network_mode;           // 网络模式
    uint32_t voice_ip;              // 语音IP
    uint16_t data_listen_port;      // 数据监听端口
    uint16_t vbus_base_port;        // 语音总线基础端口
    uint32_t net_address:24;        // 网络地址（24位）
    uint8_t base_type;              // 基站类型
    uint8_t vbus_to_chan[12];       // 语音总线到通道映射（偶数）
    uint8_t vcan_number;            // VCAN数量
    uint16_t buffertime;            // 缓冲时间
    uint16_t downtime;              // 掉线时间
    uint8_t resettime;              // 重置时间
} __attribute__((packed)) station_config_binary_t;

// 对端基站配置结构体（与deprecated/cgi/inc/1rwconference.h 100%兼容）
typedef struct {
    uint32_t peer_ip;               // 对端IP
    uint32_t peer_voice_ip;         // 对端语音IP
    uint16_t peer_data_listen_port; // 对端数据监听端口
    uint16_t peer_voice_port_base;  // 对端语音端口基址
    uint32_t peer_net_address:24;   // 对端网络地址（24位）
    uint8_t peer_type;              // 对端类型
    uint8_t peer_vbus_to_chan[12];  // 语音总线到通道映射（偶数）
} __attribute__((packed)) station_peer_config_binary_t;

// JSON格式配置（用于API接口）
typedef struct {
    uint8_t get_cfg_method;         // 获取配置方法
    uint8_t network_mode;           // 网络模式
    char voice_ip[16];              // 语音IP（字符串格式）
    uint16_t data_listen_port;      // 数据监听端口
    uint16_t vbus_base_port;        // 语音总线基础端口
    uint32_t net_address;           // 网络地址
    uint8_t base_type;              // 基站类型
    uint8_t vbus_to_chan[12];       // 语音总线到通道映射
    uint8_t vcan_number;            // VCAN数量
    uint16_t buffertime;            // 缓冲时间
    uint16_t downtime;              // 掉线时间
    uint8_t resettime;              // 重置时间
    
    // 扩展字段（用于API）
    uint32_t station_id;            // 基站ID
    char station_name[32];          // 基站名称
    uint8_t signal_strength;        // 信号强度
    uint8_t auto_register;          // 自动注册
} station_config_json_t;

// 配置操作接口
extern const config_operations_t station_config_ops;

// 核心配置操作函数
int station_config_read(station_config_binary_t *config);
int station_config_write(const station_config_binary_t *config);
int station_config_validate(const station_config_json_t *config);
int station_config_json_to_binary(const station_config_json_t *json, station_config_binary_t *binary);
int station_config_binary_to_json(const station_config_binary_t *binary, station_config_json_t *json);
void station_config_set_default(station_config_binary_t *config);

// 兼容性函数（保持与原有device_config.c的接口一致）
station_type_t detect_station_type(void);
int validate_station_config(const station_config_json_t *config);
int convert_station_json_to_binary(const station_config_json_t *json_config, station_config_binary_t *binary_config);
int convert_station_binary_to_json(const station_config_binary_t *binary_config, station_config_json_t *json_config);
int read_station_config_file(station_config_binary_t *config);
int write_station_config_file(const station_config_binary_t *config);
void set_default_station_config(station_config_binary_t *config, station_type_t station_type);
const char* get_station_type_string(station_type_t station_type);

// 对端配置函数
int station_peer_config_read(station_peer_config_binary_t *config, int peer_index);
int station_peer_config_write(const station_peer_config_binary_t *config, int peer_index);
void station_peer_config_set_default(station_peer_config_binary_t *config, station_type_t station_type);

// 工具函数
void station_ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size);
uint32_t station_ip_str_to_num(const char *ip_str);

// API处理函数声明
int station_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data);
int station_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data);
int station_handle_config_request(struct MHD_Connection *connection, const char *method, 
                                 const char *url, const char *request_data);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_STATION_STATION_CONFIG_H */ 