#include "center_config.h"
#include "config/defaults.h"
#include "utils/validation/ip_validator.h"
#include "utils/validation/common_validator.h"
#include "utils/conversion/data_converter.h"
#include "utils/file/file_utils.h"
#include "utils/network_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

/**
 * @file center_config.c
 * @brief 呼叫中心配置模块实现
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0center.c的业务逻辑，确保100%兼容
 */

// ==================== 核心配置操作函数 ====================

/**
 * 读取呼叫中心配置
 */
int center_config_read(center_config_binary_t *config) {
    return read_center_config_file(config);
}

/**
 * 写入呼叫中心配置
 */
int center_config_write(const center_config_binary_t *config) {
    return write_center_config_file(config);
}

/**
 * 验证呼叫中心配置（基于JSON格式）
 */
int center_config_validate(const center_config_json_t *config) {
    return validate_center_config(config);
}

/**
 * JSON转二进制配置
 */
int center_config_json_to_binary(const center_config_json_t *json, center_config_binary_t *binary) {
    return convert_center_json_to_binary(json, binary);
}

/**
 * 二进制转JSON配置
 */
int center_config_binary_to_json(const center_config_binary_t *binary, center_config_json_t *json) {
    return convert_center_binary_to_json(binary, json);
}

/**
 * 设置默认配置
 */
void center_config_set_default(center_config_binary_t *config) {
    set_default_center_config(config);
}

// ==================== 兼容性函数实现 ====================

/**
 * 验证呼叫中心配置数据
 * 保持与原有device_config.c中实现完全一致
 */
int validate_center_config(const center_config_json_t *config) {
    if (!config) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    
    // 验证中心号码（24位有效）- 使用统一验证
    if (validate_center_id(config->center_no) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证外部SSI（24位有效）- 使用统一验证
    if (validate_24bit_id(config->center_outssi) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证内部SSI（24位有效）- 使用统一验证
    if (validate_24bit_id(config->center_inssi) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证语音通道数（合理范围）- 使用统一验证
    if (validate_voice_channel_count(config->vchan_sum) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证端口范围 - 使用统一验证
    if (validate_voice_port(config->center_voice_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    if (validate_data_port(config->listen_agent_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    if (validate_data_port(config->send_to_agent_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    // 验证对等网络类型 - 使用统一验证
    if (!IS_VALID_PEER_NET_TYPE(config->peer_net_type)) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证IP地址格式 - 使用统一验证
    if (ip_validate_address(config->send_all_agent_ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    
    return VALIDATION_SUCCESS;
}

/**
 * JSON格式转换为二进制配置
 * 保持与原有device_config.c中实现完全一致
 */
int convert_center_json_to_binary(const center_config_json_t *json_config, 
                                center_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(center_config_binary_t));
    
    // 转换字段 - 使用统一的24位ID处理
    binary_config->center_no = data_get_24bit_value(json_config->center_no);
    binary_config->center_outssi = data_get_24bit_value(json_config->center_outssi);
    binary_config->center_inssi = data_get_24bit_value(json_config->center_inssi);
    binary_config->vchan_sum = json_config->vchan_sum;
    binary_config->center_voice_port = json_config->center_voice_port;
    binary_config->listen_agent_port = json_config->listen_agent_port;
    binary_config->peer_net_type = json_config->peer_net_type;
    binary_config->send_to_agent_port = json_config->send_to_agent_port;
    binary_config->inssi_num = json_config->inssi_num;
    binary_config->spec_function = json_config->spec_function;
    
    // 转换IP地址字符串为数字 - 使用安全接口
    uint32_t ip_num = ip_str_to_uint32_safe(json_config->send_all_agent_ip);
    if (ip_num == INADDR_NONE) {
        return CONVERTER_ERROR_FORMAT;
    }
    binary_config->send_all_agent_ip = ip_num;
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 * 保持与原有device_config.c中实现完全一致
 */
int convert_center_binary_to_json(const center_config_binary_t *binary_config, 
                                 center_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(center_config_json_t));
    
    // 转换字段
    json_config->center_no = binary_config->center_no;
    json_config->center_outssi = binary_config->center_outssi;
    json_config->center_inssi = binary_config->center_inssi;
    json_config->vchan_sum = binary_config->vchan_sum;
    json_config->center_voice_port = binary_config->center_voice_port;
    json_config->listen_agent_port = binary_config->listen_agent_port;
    json_config->peer_net_type = binary_config->peer_net_type;
    json_config->send_to_agent_port = binary_config->send_to_agent_port;
    json_config->inssi_num = binary_config->inssi_num;
    json_config->spec_function = binary_config->spec_function;
    
    // 转换IP地址数字为字符串 - 使用安全接口
    if (ip_uint32_to_str_safe(binary_config->send_all_agent_ip, json_config->send_all_agent_ip, sizeof(json_config->send_all_agent_ip)) != CONVERTER_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    return 0;
}


/**
 * 设置呼叫中心配置默认值
 * 基于原有device_config.c的实现，参考原有center.cgi的默认值设置逻辑
 */
void set_default_center_config(center_config_binary_t *config) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(center_config_binary_t));
    
    // 设置默认值 - 使用统一默认值
    config->center_no = DEFAULT_CENTER_NO;               // 使用统一默认中心号码
    config->center_outssi = DEFAULT_CENTER_NO;           // 使用统一默认外部SSI  
    config->center_inssi = DEFAULT_CENTER_NO;            // 使用统一默认内部SSI
    config->vchan_sum = DEFAULT_VCHAN_SUM;               // 使用统一默认语音通道数
    config->center_voice_port = DEFAULT_VOICE_PORT;      // 使用统一默认语音端口
    config->listen_agent_port = CALLCENTER_RECV_PORT;    // 使用统一默认监听端口
    config->peer_net_type = PEER_NET_UNICAST;            // 使用统一默认单播
    config->send_all_agent_ip = ip_str_to_uint32_safe("*************");  // 使用安全IP转换
    config->send_to_agent_port = 2900;                   // 默认发送端口
    config->inssi_num = 100;                             // 默认内应号个数
    config->spec_function = 0;                           // 默认特殊功能
}

// ==================== 工具函数 ====================


// ==================== 统一配置接口实现 ====================

/**
 * 统一配置操作接口实例
 */
const config_operations_t center_config_ops = {
    .config_name = "center",
    .config_description = "呼叫中心配置",
    .json_config_size = sizeof(center_config_json_t),
    .binary_config_size = sizeof(center_config_binary_t),
    
    .validate_json = (config_validator_func_t)center_config_validate,
    .json_to_binary = (config_converter_func_t)center_config_json_to_binary,
    .binary_to_json = (config_converter_func_t)center_config_binary_to_json,
    .read_file = (config_file_func_t)center_config_read,
    .write_file = (config_file_write_func_t)center_config_write,
    .set_default = (config_default_func_t)center_config_set_default,
    
    .custom_data = NULL
}; 