#ifndef CONFIG_COMMON_CONFIG_BASE_H
#define CONFIG_COMMON_CONFIG_BASE_H

#include <stddef.h>
#include <stdint.h>
#include <cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file config_base.h
 * @brief 配置模块统一基础框架 - 完整方案
 * <AUTHOR> Team
 * @date 2024-12-26
 * 
 * 提供统一的配置操作接口，用于所有设备配置模块
 * 解决config_operations_t冲突问题，统一所有配置模块的接口
 */

// ==================== 前向声明 ====================
struct MHD_Connection;

// ==================== 核心枚举定义 ====================

/**
 * @brief 配置处理结果枚举
 */
typedef enum {
    CONFIG_RESULT_SUCCESS = 0,          // 操作成功
    CONFIG_RESULT_INVALID_PARAM = -1,   // 参数无效
    CONFIG_RESULT_VALIDATION_FAILED = -2, // 验证失败
    CONFIG_RESULT_CONVERSION_FAILED = -3, // 转换失败
    CONFIG_RESULT_FILE_ERROR = -4,      // 文件操作错误
    CONFIG_RESULT_MEMORY_ERROR = -5,    // 内存错误
    CONFIG_RESULT_JSON_ERROR = -6,      // JSON处理错误
    CONFIG_RESULT_NETWORK_ERROR = -7,   // 网络错误
    CONFIG_RESULT_PERMISSION_ERROR = -8, // 权限错误
    CONFIG_RESULT_TIMEOUT_ERROR = -9,   // 超时错误
    CONFIG_RESULT_NOT_FOUND = -10,      // 配置未找到
    CONFIG_RESULT_ALREADY_EXISTS = -11  // 配置已存在
} config_result_t;

/**
 * @brief 配置类型枚举 - 统一定义
 */
typedef enum {
    CONFIG_TYPE_NETWORK = 0,    // 网络配置
    CONFIG_TYPE_CENTER,         // 呼叫中心配置
    CONFIG_TYPE_GATEWAY,        // 网关配置
    CONFIG_TYPE_RECORDER,       // 录音基站配置
    CONFIG_TYPE_MINI,          // 最小基站配置
    CONFIG_TYPE_SWITCH,        // 交换机配置
    CONFIG_TYPE_STATION,       // 基站配置(SCI)
    CONFIG_TYPE_SYSTEM,        // 系统配置
    CONFIG_TYPE_MAX
} config_type_t;

/**
 * @brief 设备类型枚举
 */
typedef enum {
    DEVICE_TYPE_UNKNOWN = 0x00,     // 未知设备
    DEVICE_TYPE_CENTER = 0x01,      // 呼叫中心
    DEVICE_TYPE_GATEWAY = 0x02,     // 网关
    DEVICE_TYPE_SWITCH = 0x03,      // 交换机
    DEVICE_TYPE_STATION = 0x04,     // 基站
    DEVICE_TYPE_RECORDER = 0x13,    // 录音基站
    DEVICE_TYPE_MINI = 0x17,        // 最小基站
    DEVICE_TYPE_MAX = 0xFF
} device_type_t;

/**
 * @brief 验证错误类型枚举
 */
typedef enum {
    VALIDATION_ERROR_NONE = 0,      // 无错误
    VALIDATION_ERROR_IP_INVALID,    // IP地址无效
    VALIDATION_ERROR_PORT_INVALID,  // 端口无效
    VALIDATION_ERROR_RANGE_INVALID, // 范围无效
    VALIDATION_ERROR_FORMAT_INVALID,// 格式无效
    VALIDATION_ERROR_REQUIRED_MISSING, // 必填字段缺失
    VALIDATION_ERROR_TYPE_MISMATCH  // 类型不匹配
} validation_error_t;

// ==================== 核心宏定义 ====================

// 通用配置常量
#define CONFIG_MAX_NAME_LEN         32      // 配置名称最大长度
#define CONFIG_MAX_DESC_LEN         128     // 配置描述最大长度  
#define CONFIG_MAX_PATH_LEN         256     // 文件路径最大长度
#define CONFIG_MAX_IP_LEN           16      // IP地址字符串最大长度
#define CONFIG_MAX_MAC_LEN          18      // MAC地址字符串最大长度
#define CONFIG_MAX_URL_LEN          256     // URL最大长度

// 默认配置值宏定义
#define DEFAULT_VOICE_IP            "*************"
#define DEFAULT_VOICE_PORT          3000
#define DEFAULT_DATA_PORT           2800
#define DEFAULT_SEND_PORT           2900
#define DEFAULT_CENTER_NO           0x000001
#define DEFAULT_NET_ADDRESS         0x000001
#define DEFAULT_GATEWAY_IP          "***********"
#define DEFAULT_NETMASK             "*************"
#define DEFAULT_DNS_IP              "*******"

// 24位地址掩码
#define MASK_24BIT                  0xFFFFFF

// 文件操作常量
#define CONFIG_FILE_BACKUP_SUFFIX   ".bak"
#define CONFIG_FILE_TEMP_SUFFIX     ".tmp"

// 日志级别
#define LOG_LEVEL_DEBUG             0
#define LOG_LEVEL_INFO              1
#define LOG_LEVEL_WARN              2
#define LOG_LEVEL_ERROR             3

// ==================== 函数指针类型定义 ====================

/**
 * @brief 配置验证函数类型
 * @param json_config JSON格式的配置数据
 * @return 0表示验证成功，-1表示验证失败
 */
typedef int (*config_validator_func_t)(const void *json_config);

/**
 * @brief 配置转换函数类型
 * @param input 输入数据
 * @param output 输出数据
 * @return 0表示转换成功，-1表示转换失败
 */
typedef int (*config_converter_func_t)(const void *input, void *output);

/**
 * @brief 配置文件读取函数类型
 * @param config 配置数据
 * @return 0表示操作成功，-1表示操作失败
 */
typedef int (*config_file_func_t)(void *config);

/**
 * @brief 配置文件写入函数类型
 * @param config 配置数据
 * @return 0表示操作成功，-1表示操作失败
 */
typedef int (*config_file_write_func_t)(const void *config);

/**
 * @brief 配置默认值设置函数类型
 * @param config 配置数据
 */
typedef void (*config_default_func_t)(void *config);

/**
 * @brief 配置后处理函数类型（可选）
 * @param config 配置数据
 * @return 0表示成功，非0表示失败
 */
typedef int (*config_post_process_func_t)(void *config);

// ==================== 核心结构体定义 ====================

/**
 * @brief 配置验证结果结构
 */
typedef struct {
    validation_error_t error_type;     // 错误类型
    char field_name[64];               // 出错字段名
    char error_message[256];           // 错误信息
    int line_number;                   // 出错行号（如果适用）
} config_validation_result_t;

/**
 * @brief 配置操作接口结构体（统一版本）
 * 
 * 所有设备配置模块都需要实现这个接口，提供统一的配置操作方式
 * 解决了原有的config_operations_t冲突问题
 */
typedef struct {
    // 基础信息
    const char *config_name;                    // 配置名称
    const char *config_description;             // 配置描述
    config_type_t config_type;                  // 配置类型
    device_type_t device_type;                  // 设备类型
    
    // 数据大小信息
    size_t json_config_size;                    // JSON配置结构大小
    size_t binary_config_size;                  // 二进制配置结构大小
    
    // 核心操作函数
    config_validator_func_t validate_json;      // JSON配置验证
    config_converter_func_t json_to_binary;     // JSON → 二进制转换
    config_converter_func_t binary_to_json;     // 二进制 → JSON转换
    config_file_func_t read_file;               // 读取配置文件
    config_file_write_func_t write_file;        // 写入配置文件
    config_default_func_t set_default;          // 设置默认配置
    
    // 扩展功能（可选）
    config_post_process_func_t post_read;       // 读取后处理
    config_post_process_func_t pre_write;       // 写入前处理
    void *custom_data;                          // 自定义数据指针
    
    // 版本信息
    uint16_t version_major;                     // 主版本号
    uint16_t version_minor;                     // 次版本号
} config_operations_t;

/**
 * @brief 配置响应数据结构
 */
typedef struct {
    config_result_t result;             // 操作结果
    char *message;                      // 响应消息
    cJSON *data;                        // 响应数据
    long timestamp;                     // 时间戳
    config_validation_result_t *validation_errors; // 验证错误详情
    int error_count;                    // 错误数量
} config_response_t;

/**
 * @brief 配置文件信息结构
 */
typedef struct {
    char file_path[CONFIG_MAX_PATH_LEN];        // 文件路径
    size_t file_size;                           // 文件大小
    long last_modified;                         // 最后修改时间
    uint32_t checksum;                          // 文件校验和
    int is_backup_available;                    // 是否有备份可用
} config_file_info_t;

/**
 * @brief 配置统计信息结构
 */
typedef struct {
    int read_count;                     // 读取次数
    int write_count;                    // 写入次数
    int validation_failures;            // 验证失败次数
    long last_access_time;              // 最后访问时间
    long last_modify_time;              // 最后修改时间
} config_stats_t;

// ==================== 通用配置处理接口 ====================

/**
 * @brief 处理配置获取请求
 * @param ops 配置操作接口
 * @param response 输出的响应数据
 * @return config_result_t 操作结果
 */
config_result_t config_process_get_request(const config_operations_t *ops, 
                                          config_response_t *response);

/**
 * @brief 处理配置保存请求
 * @param ops 配置操作接口
 * @param request_json 输入的JSON请求数据
 * @param response 输出的响应数据
 * @return config_result_t 操作结果
 */
config_result_t config_process_post_request(const config_operations_t *ops, 
                                           const cJSON *request_json,
                                           config_response_t *response);

/**
 * @brief 验证配置数据
 * @param ops 配置操作接口
 * @param json_config JSON格式的配置数据
 * @param validation_results 输出验证结果详情（可为NULL）
 * @return config_result_t 验证结果
 */
config_result_t config_validate_data(const config_operations_t *ops,
                                    const void *json_config,
                                    config_validation_result_t **validation_results);

/**
 * @brief 获取配置默认值
 * @param ops 配置操作接口
 * @param response 输出的响应数据（包含默认配置）
 * @return config_result_t 操作结果
 */
config_result_t config_get_default(const config_operations_t *ops,
                                  config_response_t *response);

/**
 * @brief 备份配置文件
 * @param ops 配置操作接口
 * @param backup_suffix 备份后缀（可为NULL，使用默认）
 * @return config_result_t 操作结果
 */
config_result_t config_backup_file(const config_operations_t *ops,
                                  const char *backup_suffix);

/**
 * @brief 恢复配置文件
 * @param ops 配置操作接口
 * @param backup_suffix 备份后缀（可为NULL，使用默认）
 * @return config_result_t 操作结果
 */
config_result_t config_restore_file(const config_operations_t *ops,
                                   const char *backup_suffix);

// ==================== 响应处理接口 ====================

/**
 * @brief 创建配置响应
 * @param result 操作结果
 * @param message 响应消息
 * @param data 响应数据（可以为NULL）
 * @return config_response_t* 响应对象，使用后需要调用config_response_destroy释放
 */
config_response_t* config_response_create(config_result_t result, 
                                         const char *message, 
                                         cJSON *data);

/**
 * @brief 销毁配置响应
 * @param response 响应对象
 */
void config_response_destroy(config_response_t *response);

/**
 * @brief 将配置响应转换为JSON格式
 * @param response 响应对象
 * @return cJSON* JSON对象，使用后需要调用cJSON_Delete释放
 */
cJSON* config_response_to_json(const config_response_t *response);

/**
 * @brief 添加验证错误到响应
 * @param response 响应对象
 * @param error_type 错误类型
 * @param field_name 字段名
 * @param error_message 错误信息
 * @return config_result_t 操作结果
 */
config_result_t config_response_add_validation_error(config_response_t *response,
                                                    validation_error_t error_type,
                                                    const char *field_name,
                                                    const char *error_message);

// ==================== 工具函数 ====================

/**
 * @brief 获取配置结果的字符串描述
 * @param result 配置结果
 * @return const char* 结果描述字符串
 */
const char* config_result_to_string(config_result_t result);

/**
 * @brief 获取配置类型的字符串描述
 * @param type 配置类型
 * @return const char* 类型描述字符串
 */
const char* config_type_to_string(config_type_t type);

/**
 * @brief 获取设备类型的字符串描述
 * @param type 设备类型
 * @return const char* 设备类型描述字符串
 */
const char* device_type_to_string(device_type_t type);

/**
 * @brief 检查配置操作接口的完整性
 * @param ops 配置操作接口
 * @return 1表示接口完整，0表示接口不完整
 */
int config_operations_validate(const config_operations_t *ops);

/**
 * @brief 创建标准错误响应
 * @param result 错误类型
 * @param error_message 错误消息
 * @return config_response_t* 错误响应对象
 */
config_response_t* config_create_error_response(config_result_t result,
                                               const char *error_message);

/**
 * @brief 创建标准成功响应
 * @param message 成功消息
 * @param data 响应数据
 * @return config_response_t* 成功响应对象
 */
config_response_t* config_create_success_response(const char *message,
                                                 cJSON *data);

/**
 * @brief 检查24位地址有效性
 * @param address 待检查的地址
 * @return 1表示有效，0表示无效
 */
int config_is_valid_24bit_address(uint32_t address);

/**
 * @brief 检查端口号有效性
 * @param port 待检查的端口号
 * @return 1表示有效，0表示无效
 */
int config_is_valid_port(uint16_t port);

/**
 * @brief 获取当前时间戳
 * @return long 时间戳
 */
long config_get_timestamp(void);

/**
 * @brief 生成配置文件校验和
 * @param file_path 文件路径
 * @return uint32_t 校验和，0表示失败
 */
uint32_t config_calculate_checksum(const char *file_path);

/**
 * @brief 获取配置文件信息
 * @param file_path 文件路径
 * @param info 输出文件信息
 * @return config_result_t 操作结果
 */
config_result_t config_get_file_info(const char *file_path, config_file_info_t *info);

// ==================== 配置管理器接口 ====================

/**
 * @brief 注册配置操作接口
 * @param ops 配置操作接口
 * @return config_result_t 操作结果
 */
config_result_t config_manager_register(const config_operations_t *ops);

/**
 * @brief 注销配置操作接口
 * @param config_type 配置类型
 * @return config_result_t 操作结果
 */
config_result_t config_manager_unregister(config_type_t config_type);

/**
 * @brief 获取配置操作接口
 * @param config_type 配置类型
 * @return const config_operations_t* 配置操作接口，NULL表示未找到
 */
const config_operations_t* config_manager_get_operations(config_type_t config_type);

/**
 * @brief 列出所有已注册的配置类型
 * @param types 输出配置类型数组
 * @param max_count 数组最大容量
 * @return int 实际配置类型数量
 */
int config_manager_list_types(config_type_t *types, int max_count);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_COMMON_CONFIG_BASE_H */ 