/*******************************************************************
 * File          : gateway_api.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 网关配置API处理模块 - 基于deprecated/cgi/0gateway.c重构
 * 提供RESTful API接口处理网关配置的读取和写入
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : created from deprecated/cgi/0gateway.c
 *******************************************************************/

#include "config/gateway/gateway_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <cJSON.h>

/**
 * 处理网关配置GET请求
 */
int handle_gateway_config_get(char **response) {
    if (!response) {
        return -1;
    }
    
    gateway_config_t binary_config;
    gateway_config_json_t json_config;
    
    // 读取配置文件
    int ret = read_gateway_config_file(&binary_config);
    if (ret < 0) {
        // 使用默认配置
        set_default_gateway_config(&binary_config);
    }
    
    // 转换为JSON格式
    ret = convert_gateway_binary_to_json(&binary_config, &json_config);
    if (ret < 0) {
        return -1;
    }
    
    // 创建JSON响应
    cJSON *json = cJSON_CreateObject();
    if (!json) {
        return -1;
    }
    
    // 网络配置
    cJSON *network = cJSON_CreateObject();
    cJSON_AddStringToObject(network, "ip", json_config.ip);
    cJSON_AddStringToObject(network, "mask", json_config.mask);
    cJSON_AddStringToObject(network, "gateway", json_config.gateway_addr);
    cJSON_AddStringToObject(network, "dns", json_config.dns);
    cJSON_AddStringToObject(network, "mac", json_config.mac);
    cJSON_AddItemToObject(json, "network", network);
    
    // 中心配置
    cJSON *center = cJSON_CreateObject();
    cJSON_AddNumberToObject(center, "center_no", json_config.center_no);
    cJSON_AddNumberToObject(center, "center_outssi", json_config.center_outssi);
    cJSON_AddNumberToObject(center, "center_inssi", json_config.center_inssi);
    cJSON_AddNumberToObject(center, "vchan_sum", json_config.vchan_sum);
    cJSON_AddNumberToObject(center, "center_voice_port", json_config.center_voice_port);
    cJSON_AddNumberToObject(center, "listen_agent_port", json_config.listen_agent_port);
    cJSON_AddNumberToObject(center, "peer_net_type", json_config.peer_net_type);
    cJSON_AddStringToObject(center, "send_all_agent_ip", json_config.send_all_agent_ip);
    cJSON_AddNumberToObject(center, "send_to_agent_port", json_config.send_to_agent_port);
    cJSON_AddNumberToObject(center, "inssi_num", json_config.inssi_num);
    cJSON_AddNumberToObject(center, "spec_function", json_config.spec_function);
    cJSON_AddItemToObject(json, "center", center);
    
    // 基础配置
    cJSON *basic = cJSON_CreateObject();
    cJSON_AddStringToObject(basic, "daemon_ip", json_config.daemon_ip);
    cJSON_AddNumberToObject(basic, "daemon_port", json_config.daemon_port);
    cJSON_AddStringToObject(basic, "log_ip", json_config.log_ip);
    cJSON_AddNumberToObject(basic, "log_port", json_config.log_port);
    cJSON_AddNumberToObject(basic, "log_level", json_config.log_level);
    cJSON_AddNumberToObject(basic, "log_to_where", json_config.log_to_where);
    cJSON_AddNumberToObject(basic, "data_listen_port", json_config.data_listen_port);
    cJSON_AddNumberToObject(basic, "data_send_port", json_config.data_send_port);
    cJSON_AddItemToObject(json, "basic", basic);
    
    // 会议配置
    cJSON *conference = cJSON_CreateObject();
    cJSON_AddNumberToObject(conference, "get_cfg_method", json_config.get_cfg_method);
    cJSON_AddNumberToObject(conference, "network_mode", json_config.network_mode);
    cJSON_AddNumberToObject(conference, "vbus_base_port", json_config.vbus_base_port);
    cJSON_AddNumberToObject(conference, "net_address", json_config.net_address);
    cJSON_AddNumberToObject(conference, "base_type", json_config.base_type);
    cJSON_AddNumberToObject(conference, "vcan_number", json_config.vcan_number);
    cJSON_AddNumberToObject(conference, "buffertime", json_config.buffertime);
    cJSON_AddNumberToObject(conference, "downtime", json_config.downtime);
    cJSON_AddNumberToObject(conference, "resettime", json_config.resettime);
    cJSON_AddNumberToObject(conference, "peer_base_num", json_config.peer_base_num);
    cJSON_AddItemToObject(json, "conference", conference);
    
    // 编解码配置
    cJSON *vocoder = cJSON_CreateObject();
    cJSON_AddNumberToObject(vocoder, "amp", json_config.amp);
    cJSON_AddNumberToObject(vocoder, "cs", json_config.cs);
    cJSON_AddNumberToObject(vocoder, "pcm", json_config.pcm);
    cJSON_AddNumberToObject(vocoder, "vocoder", json_config.vocoder);
    cJSON_AddItemToObject(json, "vocoder", vocoder);
    
    // 基站配置
    cJSON *base = cJSON_CreateObject();
    cJSON_AddNumberToObject(base, "syscode", json_config.syscode);
    
    // 频率配置
    cJSON *frequencies = cJSON_CreateArray();
    for (int i = 0; i < CFG_FREQ_SUM; i++) {
        cJSON_AddItemToArray(frequencies, cJSON_CreateNumber(json_config.frequencies[i]));
    }
    cJSON_AddItemToObject(base, "frequencies", frequencies);
    cJSON_AddItemToObject(json, "base", base);
    
    // 转换为字符串
    *response = cJSON_Print(json);
    cJSON_Delete(json);
    
    return (*response != NULL) ? 0 : -1;
}

/**
 * 处理网关配置POST请求
 */
int handle_gateway_config_post(const char *request_body, char **response) {
    if (!request_body || !response) {
        return -1;
    }
    
    // 解析JSON请求
    cJSON *json = cJSON_Parse(request_body);
    if (!json) {
        return -1;
    }
    
    gateway_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 解析网络配置
    cJSON *network = cJSON_GetObjectItem(json, "network");
    if (network) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(network, "ip"))) {
            strncpy(json_config.ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "mask"))) {
            strncpy(json_config.mask, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "gateway"))) {
            strncpy(json_config.gateway_addr, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "dns"))) {
            strncpy(json_config.dns, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(network, "mac"))) {
            strncpy(json_config.mac, item->valuestring, LEN_MAC_ADDR-1);
        }
    }
    
    // 解析中心配置
    cJSON *center = cJSON_GetObjectItem(json, "center");
    if (center) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(center, "center_no"))) {
            json_config.center_no = (uint32_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "center_outssi"))) {
            json_config.center_outssi = (uint32_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "center_inssi"))) {
            json_config.center_inssi = (uint32_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "vchan_sum"))) {
            json_config.vchan_sum = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "center_voice_port"))) {
            json_config.center_voice_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "listen_agent_port"))) {
            json_config.listen_agent_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "peer_net_type"))) {
            json_config.peer_net_type = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "send_all_agent_ip"))) {
            strncpy(json_config.send_all_agent_ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(center, "send_to_agent_port"))) {
            json_config.send_to_agent_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "inssi_num"))) {
            json_config.inssi_num = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(center, "spec_function"))) {
            json_config.spec_function = (uint16_t)item->valuedouble;
        }
    }
    
    // 解析基础配置
    cJSON *basic = cJSON_GetObjectItem(json, "basic");
    if (basic) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(basic, "daemon_ip"))) {
            strncpy(json_config.daemon_ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(basic, "daemon_port"))) {
            json_config.daemon_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_ip"))) {
            strncpy(json_config.log_ip, item->valuestring, LEN_IP_ADDR-1);
        }
        if ((item = cJSON_GetObjectItem(basic, "log_port"))) {
            json_config.log_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_level"))) {
            json_config.log_level = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "log_to_where"))) {
            json_config.log_to_where = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "data_listen_port"))) {
            json_config.data_listen_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(basic, "data_send_port"))) {
            json_config.data_send_port = (uint16_t)item->valuedouble;
        }
    }
    
    // 解析会议配置
    cJSON *conference = cJSON_GetObjectItem(json, "conference");
    if (conference) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(conference, "get_cfg_method"))) {
            json_config.get_cfg_method = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "network_mode"))) {
            json_config.network_mode = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "vbus_base_port"))) {
            json_config.vbus_base_port = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "net_address"))) {
            json_config.net_address = (uint32_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "base_type"))) {
            json_config.base_type = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "vcan_number"))) {
            json_config.vcan_number = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "buffertime"))) {
            json_config.buffertime = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "downtime"))) {
            json_config.downtime = (uint16_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "resettime"))) {
            json_config.resettime = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(conference, "peer_base_num"))) {
            json_config.peer_base_num = (uint8_t)item->valuedouble;
        }
    }
    
    // 解析编解码配置
    cJSON *vocoder = cJSON_GetObjectItem(json, "vocoder");
    if (vocoder) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(vocoder, "amp"))) {
            json_config.amp = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(vocoder, "cs"))) {
            json_config.cs = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(vocoder, "pcm"))) {
            json_config.pcm = (uint8_t)item->valuedouble;
        }
        if ((item = cJSON_GetObjectItem(vocoder, "vocoder"))) {
            json_config.vocoder = (uint8_t)item->valuedouble;
        }
    }
    
    // 解析基站配置
    cJSON *base = cJSON_GetObjectItem(json, "base");
    if (base) {
        cJSON *item;
        if ((item = cJSON_GetObjectItem(base, "syscode"))) {
            json_config.syscode = (uint16_t)item->valuedouble;
        }
        
        cJSON *frequencies = cJSON_GetObjectItem(base, "frequencies");
        if (frequencies && cJSON_IsArray(frequencies)) {
            int size = cJSON_GetArraySize(frequencies);
            for (int i = 0; i < size && i < CFG_FREQ_SUM; i++) {
                cJSON *freq = cJSON_GetArrayItem(frequencies, i);
                if (freq) {
                    json_config.frequencies[i] = (uint16_t)freq->valuedouble;
                }
            }
        }
    }
    
    cJSON_Delete(json);
    
    // 验证配置数据
    int ret = validate_gateway_config(&json_config);
    if (ret < 0) {
        *response = strdup("{\"error\":\"Invalid configuration data\"}");
        return -1;
    }
    
    // 转换为二进制格式
    gateway_config_t binary_config;
    ret = convert_gateway_json_to_binary(&json_config, &binary_config);
    if (ret < 0) {
        *response = strdup("{\"error\":\"Configuration conversion failed\"}");
        return -1;
    }
    
    // 写入配置文件
    ret = write_gateway_config_file(&binary_config);
    if (ret < 0) {
        *response = strdup("{\"error\":\"Failed to save configuration\"}");
        return -1;
    }
    
    // 返回成功响应
    *response = strdup("{\"status\":\"success\",\"message\":\"Gateway configuration saved successfully\"}");
    return 0;
}
