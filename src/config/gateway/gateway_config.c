/*******************************************************************
 * File          : gateway_config.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 网关配置模块实现 - 基于deprecated/cgi/0gateway.c重构
 * 100%兼容原有网关配置功能和数据结构
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : created from deprecated/cgi/0gateway.c
 *******************************************************************/

#include "config/gateway/gateway_config.h"
#include "config/defaults.h"
#include "utils/file/file_utils.h"
#include "utils/file/config_file_manager.h"
#include "utils/validation/common_validator.h"
#include "utils/conversion/data_converter.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>
#include <sys/stat.h>

// 工具函数声明
static void eth_str_to_num(const gateway_net_config_t *pConfig, gateway_cfg_net_t *pNet);
static void eth_num_to_str(const gateway_cfg_net_t *pNet, gateway_net_config_t *pConfig);
static int read_configure(uint8_t device_type, uint32_t offset, size_t size, void *data);
static int write_configure(uint8_t device_type, uint32_t offset, size_t size, const void *data);
static int read_eth_config(gateway_board_net_t *pBoard, int index);
static int write_eth_config(const gateway_board_net_t *pBoard, int index);

// 配置文件相关定义 - 使用统一的defaults.h
#define START_ADDR_COMMONCFG    0x00
#define START_ADDR_BOARD_BASIC  0x10
#define START_ADDR_CONF         0x40
#define START_ADDR_CONF_PEER    0x80
#define START_ADDR_CALLCENTER   0x00
#define START_ADDR_VOCODER      0xC0
#define START_ADDR_BASE         0xE0
#define START_ADDR_FREQ         0xF0

#define FILE_LEN_OF_COMMONCFG   256
#define FILE_LEN_OF_BOARDCFG    512

/**
 * 验证网关配置数据 - 使用统一验证工具
 */
int validate_gateway_config(const gateway_config_json_t *config) {
    if (!config) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    
    // 验证中心号码（24位有效）
    if (validate_center_id(config->center_no) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证外部SSI（24位有效）
    if (validate_24bit_id(config->center_outssi) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证内部SSI（24位有效）
    if (validate_24bit_id(config->center_inssi) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证语音通道数（合理范围）
    if (validate_voice_channel_count(config->vchan_sum) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证网络地址（24位有效）
    if (validate_24bit_id(config->net_address) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    
    // 验证IP地址格式 - 使用统一接口
    if (ip_validate_address(config->ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_netmask(config->mask) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_address(config->gateway_addr) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_address(config->dns) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_address(config->daemon_ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_address(config->log_ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    if (ip_validate_address(config->send_all_agent_ip) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_IP_INVALID;
    }
    
    // 验证端口号
    if (validate_port_number(config->daemon_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    if (validate_port_number(config->log_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    if (validate_data_port(config->data_listen_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    if (validate_voice_port(config->center_voice_port) != VALIDATION_SUCCESS) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    
    return VALIDATION_SUCCESS;
}

/**
 * JSON转二进制配置
 */
int convert_gateway_json_to_binary(const gateway_config_json_t *json_config, 
                                  gateway_config_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(gateway_config_t));
    
    // 网络配置转换
    strncpy(binary_config->board_net.ethconfig.ip, json_config->ip, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.mask, json_config->mask, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.gateway, json_config->gateway_addr, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.dns, json_config->dns, LEN_IP_ADDR-1);
    strncpy(binary_config->board_net.ethconfig.mac, json_config->mac, LEN_MAC_ADDR-1);
    binary_config->board_net.wlan_enable_val = 0;
    
    // 转换为数字格式
    eth_str_to_num(&binary_config->board_net.ethconfig, &binary_config->cfg_net);
    
    // 通用配置
    binary_config->cfg_common.ds = 1;
    binary_config->cfg_common.sw = 1;
    binary_config->cfg_common.conf_num = 4;
    binary_config->cfg_common.normal_num = 16;
    
    // 板卡基础配置 - 使用安全IP转换
    binary_config->cfg_board_basic.daemon_ip = ip_str_to_uint32_safe(json_config->daemon_ip);
    binary_config->cfg_board_basic.daemon_port = json_config->daemon_port;
    binary_config->cfg_board_basic.log_ip = ip_str_to_uint32_safe(json_config->log_ip);
    binary_config->cfg_board_basic.log_port = json_config->log_port;
    binary_config->cfg_board_basic.cfg_ip = binary_config->cfg_net.ip;
    binary_config->cfg_board_basic.cfg_port = 80;
    binary_config->cfg_board_basic.log_level = json_config->log_level;
    binary_config->cfg_board_basic.log_to_where = json_config->log_to_where;
    binary_config->cfg_board_basic.data_listen_port = json_config->data_listen_port;
    binary_config->cfg_board_basic.data_send_port = json_config->data_send_port;
    
    // 会议配置
    binary_config->cfg_conf.get_cfg_method = json_config->get_cfg_method;
    binary_config->cfg_conf.network_mode = json_config->network_mode;
    binary_config->cfg_conf.voice_ip = binary_config->cfg_net.ip;
    binary_config->cfg_conf.data_listen_port = json_config->data_listen_port;
    binary_config->cfg_conf.vbus_base_port = json_config->vbus_base_port;
    binary_config->cfg_conf.net_address = data_get_24bit_value(json_config->net_address);
    binary_config->cfg_conf.base_type = json_config->base_type;
    binary_config->cfg_conf.vcan_number = json_config->vcan_number;
    binary_config->cfg_conf.buffertime = json_config->buffertime;
    binary_config->cfg_conf.downtime = json_config->downtime;
    binary_config->cfg_conf.resettime = json_config->resettime;
    binary_config->cfg_conf.peer_base_num = json_config->peer_base_num;
    binary_config->cfg_conf.spec_function = json_config->spec_function;
    
    // 对端基站配置 - 使用统一默认值
    binary_config->cfg_peer_base.peer_ip = binary_config->cfg_net.ip;
    binary_config->cfg_peer_base.peer_voice_ip = binary_config->cfg_net.ip;
    binary_config->cfg_peer_base.peer_type = DEVICE_TYPE_PDT;
    binary_config->cfg_peer_base.peer_voice_port_base = FUNCTION_VOICE_SWITCH_PORT_BASE;
    binary_config->cfg_peer_base.peer_data_listen_port = CALLCENTER_RECV_PORT;
    binary_config->cfg_peer_base.peer_net_address = data_get_24bit_value(json_config->center_no);
    
    // 呼叫中心配置 - 使用统一24位ID处理
    binary_config->cfg_center.center_no = data_get_24bit_value(json_config->center_no);
    binary_config->cfg_center.center_outssi = data_get_24bit_value(json_config->center_outssi);
    binary_config->cfg_center.center_inssi = data_get_24bit_value(json_config->center_inssi);
    binary_config->cfg_center.vchan_sum = json_config->vchan_sum;
    binary_config->cfg_center.center_voice_port = json_config->center_voice_port;
    binary_config->cfg_center.listen_agent_port = json_config->listen_agent_port;
    binary_config->cfg_center.peer_net_type = json_config->peer_net_type;
    binary_config->cfg_center.send_all_agent_ip = ip_str_to_uint32_safe(json_config->send_all_agent_ip);
    binary_config->cfg_center.send_to_agent_port = json_config->send_to_agent_port;
    binary_config->cfg_center.inssi_num = json_config->inssi_num;
    binary_config->cfg_center.spec_function = json_config->spec_function;
    
    // 编解码配置
    binary_config->cfg_vocoder.amp = json_config->amp;
    binary_config->cfg_vocoder.cs = json_config->cs;
    binary_config->cfg_vocoder.pcm = json_config->pcm;
    binary_config->cfg_vocoder.vocoder = json_config->vocoder;
    
    // 基站配置
    binary_config->cfg_base.syscode = json_config->syscode;
    
    // 频率配置
    for (int i = 0; i < CFG_FREQ_SUM; i++) {
        binary_config->cfg_freq.freq[i] = json_config->frequencies[i];
    }
    
    return 0;
}

/**
 * 二进制转JSON配置
 */
int convert_gateway_binary_to_json(const gateway_config_t *binary_config, 
                                  gateway_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(gateway_config_json_t));
    
    // 网络配置转换
    strncpy(json_config->ip, binary_config->board_net.ethconfig.ip, LEN_IP_ADDR-1);
    strncpy(json_config->mask, binary_config->board_net.ethconfig.mask, LEN_IP_ADDR-1);
    strncpy(json_config->gateway_addr, binary_config->board_net.ethconfig.gateway, LEN_IP_ADDR-1);
    strncpy(json_config->dns, binary_config->board_net.ethconfig.dns, LEN_IP_ADDR-1);
    strncpy(json_config->mac, binary_config->board_net.ethconfig.mac, LEN_MAC_ADDR-1);
    
    // 板卡基础配置转换
    struct in_addr addr;
    addr.s_addr = binary_config->cfg_board_basic.daemon_ip;
    strncpy(json_config->daemon_ip, inet_ntoa(addr), LEN_IP_ADDR-1);
    json_config->daemon_port = binary_config->cfg_board_basic.daemon_port;
    
    addr.s_addr = binary_config->cfg_board_basic.log_ip;
    strncpy(json_config->log_ip, inet_ntoa(addr), LEN_IP_ADDR-1);
    json_config->log_port = binary_config->cfg_board_basic.log_port;
    json_config->log_level = binary_config->cfg_board_basic.log_level;
    json_config->log_to_where = binary_config->cfg_board_basic.log_to_where;
    json_config->data_listen_port = binary_config->cfg_board_basic.data_listen_port;
    json_config->data_send_port = binary_config->cfg_board_basic.data_send_port;
    
    // 会议配置转换
    json_config->get_cfg_method = binary_config->cfg_conf.get_cfg_method;
    json_config->network_mode = binary_config->cfg_conf.network_mode;
    json_config->vbus_base_port = binary_config->cfg_conf.vbus_base_port;
    json_config->net_address = binary_config->cfg_conf.net_address;
    json_config->base_type = binary_config->cfg_conf.base_type;
    json_config->vcan_number = binary_config->cfg_conf.vcan_number;
    json_config->buffertime = binary_config->cfg_conf.buffertime;
    json_config->downtime = binary_config->cfg_conf.downtime;
    json_config->resettime = binary_config->cfg_conf.resettime;
    json_config->peer_base_num = binary_config->cfg_conf.peer_base_num;
    
    // 呼叫中心配置转换
    json_config->center_no = binary_config->cfg_center.center_no;
    json_config->center_outssi = binary_config->cfg_center.center_outssi;
    json_config->center_inssi = binary_config->cfg_center.center_inssi;
    json_config->vchan_sum = binary_config->cfg_center.vchan_sum;
    json_config->center_voice_port = binary_config->cfg_center.center_voice_port;
    json_config->listen_agent_port = binary_config->cfg_center.listen_agent_port;
    json_config->peer_net_type = binary_config->cfg_center.peer_net_type;
    
    addr.s_addr = binary_config->cfg_center.send_all_agent_ip;
    strncpy(json_config->send_all_agent_ip, inet_ntoa(addr), LEN_IP_ADDR-1);
    json_config->send_to_agent_port = binary_config->cfg_center.send_to_agent_port;
    json_config->inssi_num = binary_config->cfg_center.inssi_num;
    json_config->spec_function = binary_config->cfg_center.spec_function;
    
    // 编解码配置转换
    json_config->amp = binary_config->cfg_vocoder.amp;
    json_config->cs = binary_config->cfg_vocoder.cs;
    json_config->pcm = binary_config->cfg_vocoder.pcm;
    json_config->vocoder = binary_config->cfg_vocoder.vocoder;
    
    // 基站配置转换
    json_config->syscode = binary_config->cfg_base.syscode;
    
    // 频率配置转换
    for (int i = 0; i < CFG_FREQ_SUM; i++) {
        json_config->frequencies[i] = binary_config->cfg_freq.freq[i];
    }
    
    return 0;
}

/**
 * 读取网关配置文件 - 基于deprecated/cgi/0gateway.c的gateway_read_cfg
 */
int read_gateway_config_file(gateway_config_t *config) {
    if (!config) {
        return -1;
    }

    int ret;
    int checkret = GATEWAY_READ_SUCCESS;

    // 清零配置结构
    memset(config, 0, sizeof(gateway_config_t));

    // 读取网络配置文件
    ret = read_eth_config(&config->board_net, 0);
    if (ret == -1) {
        checkret |= GATEWAY_OPEN_ETH_ERR;
        // 设置默认网络配置
        strcpy(config->board_net.ethconfig.ip, "***********00");
        strcpy(config->board_net.ethconfig.mask, "*************");
        strcpy(config->board_net.ethconfig.gateway, "***********");
        strcpy(config->board_net.ethconfig.dns, "*******");
        strcpy(config->board_net.ethconfig.mac, "00:11:22:33:44:55");
        config->board_net.wlan_enable_val = 0;
    }
    eth_str_to_num(&config->board_net.ethconfig, &config->cfg_net);

    // 读取通用配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_COMMONCFG,
                        sizeof(gateway_cfg_common_t), &config->cfg_common);
    if (ret < 0) {
        checkret |= OPEN_COMMON_ERR;
        // 设置默认值并创建文件
        config->cfg_common.ds = 1;
        config->cfg_common.sw = 1;
        config->cfg_common.conf_num = 4;
        config->cfg_common.normal_num = 16;
        write_configure(DEVICE_TYPE_SWITCH, START_ADDR_COMMONCFG,
                       sizeof(gateway_cfg_common_t), &config->cfg_common);
    }

    // 读取板卡基础配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_BOARD_BASIC,
                        sizeof(gateway_cfg_board_basic_t), &config->cfg_board_basic);
    if (ret < 0) {
        checkret |= OPEN_BASIC_ERR;
        // 设置默认值 - 使用统一默认值
        config->cfg_board_basic.daemon_ip = config->cfg_net.ip;
        config->cfg_board_basic.daemon_port = 2500;
        config->cfg_board_basic.log_ip = config->cfg_net.ip;
        config->cfg_board_basic.log_port = 514;
        config->cfg_board_basic.cfg_ip = config->cfg_net.ip;
        config->cfg_board_basic.cfg_port = 80;
        config->cfg_board_basic.log_level = 3;
        config->cfg_board_basic.log_to_where = 0;
        config->cfg_board_basic.data_listen_port = DEFAULT_DATA_LISTEN_PORT;
        config->cfg_board_basic.data_send_port = 2601;
        write_configure(DEVICE_TYPE_SWITCH, START_ADDR_BOARD_BASIC,
                       sizeof(gateway_cfg_board_basic_t), &config->cfg_board_basic);
    }

    // 读取会议配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_CONF,
                        sizeof(gateway_cfg_conf_t), &config->cfg_conf);
    if (ret < 0) {
        checkret |= OPEN_CONF_ERR;
        // 设置默认值 - 使用统一默认值
        config->cfg_conf.get_cfg_method = 0;
        config->cfg_conf.network_mode = 0;
        config->cfg_conf.voice_ip = config->cfg_net.ip;
        config->cfg_conf.data_listen_port = DEFAULT_DATA_LISTEN_PORT;
        config->cfg_conf.vbus_base_port = VOICE_PORT_BASE;
        config->cfg_conf.net_address = DEFAULT_NET_ADDRESS;
        config->cfg_conf.base_type = DEVICE_TYPE_SWITCH;
        config->cfg_conf.vcan_number = 1;
        config->cfg_conf.buffertime = DEFAULT_BUFFER_TIME;
        config->cfg_conf.downtime = DEFAULT_VOICE_DOWN_TIME;
        config->cfg_conf.resettime = 60;
        config->cfg_conf.peer_base_num = 1;
        config->cfg_conf.spec_function = 0;
    }

    // 读取对端基站配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_CONF_PEER,
                        sizeof(gateway_cfg_peer_base_t), &config->cfg_peer_base);
    if (ret < 0) {
        checkret |= OPEN_CONF_ERR;
        // 设置默认值 - 使用统一默认值
        config->cfg_peer_base.peer_ip = config->cfg_net.ip;
        config->cfg_peer_base.peer_voice_ip = config->cfg_net.ip;
        config->cfg_peer_base.peer_type = DEVICE_TYPE_PDT;
        config->cfg_peer_base.peer_voice_port_base = FUNCTION_VOICE_SWITCH_PORT_BASE;
        config->cfg_peer_base.peer_data_listen_port = CALLCENTER_RECV_PORT;
        config->cfg_peer_base.peer_net_address = DEFAULT_NET_ADDRESS;
    }

    // 读取呼叫中心配置
    ret = read_configure(DEVICE_TYPE_CALLCENTER, START_ADDR_CALLCENTER,
                        sizeof(gateway_cfg_center_t), &config->cfg_center);
    if (ret < 0) {
        checkret |= OPEN_CENTER_ERR;
        // 设置默认值 - 使用统一默认值
        config->cfg_center.center_no = DEFAULT_CENTER_NO;
        config->cfg_center.center_outssi = DEFAULT_CENTER_NO;
        config->cfg_center.center_inssi = DEFAULT_CENTER_NO;
        config->cfg_center.vchan_sum = DEFAULT_VCHAN_SUM;
        config->cfg_center.center_voice_port = DEFAULT_VOICE_PORT;
        config->cfg_center.listen_agent_port = CALLCENTER_RECV_PORT;
        config->cfg_center.peer_net_type = PEER_NET_UNICAST;
        config->cfg_center.send_all_agent_ip = config->cfg_net.ip;
        config->cfg_center.send_to_agent_port = CALLCENTER_RECV_PORT;
        config->cfg_center.inssi_num = 100;
        config->cfg_center.spec_function = 0;
    }

    // 读取编解码配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_VOCODER,
                        sizeof(gateway_cfg_vocoder_t), &config->cfg_vocoder);
    if (ret < 0) {
        checkret |= OPEN_VOCODER_ERR;
        // 设置默认值
        config->cfg_vocoder.amp = 0;
        config->cfg_vocoder.cs = 0;
        config->cfg_vocoder.pcm = 0;
        config->cfg_vocoder.vocoder = 0;
    }

    // 读取基站配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_BASE,
                        sizeof(gateway_cfg_base_t), &config->cfg_base);
    if (ret < 0) {
        checkret |= OPEN_BASE_ERR;
        // 设置默认值
        config->cfg_base.syscode = 0x0001;
    }

    // 读取频率配置
    ret = read_configure(DEVICE_TYPE_SWITCH, START_ADDR_FREQ,
                        sizeof(gateway_cfg_freq_t), &config->cfg_freq);
    if (ret < 0) {
        checkret |= OPEN_BASE_ERR;
        // 设置默认频率值
        for (int i = 0; i < CFG_FREQ_SUM; i++) {
            config->cfg_freq.freq[i] = 0x1C00 + i;  // 默认频率值
        }
    }

    return checkret;
}

/**
 * 写入网关配置文件 - 基于deprecated/cgi/0gateway.c的gateway_write_cfg
 */
int write_gateway_config_file(const gateway_config_t *config) {
    if (!config) {
        return -1;
    }

    int ret;
    int checkret = GATEWAY_READ_SUCCESS;

    // 写入网络配置
    ret = write_eth_config(&config->board_net, 0);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_ETH_ERR;
    }

    // 写入通用配置
    ret = write_configure(DEVICE_TYPE_SWITCH, START_ADDR_COMMONCFG,
                         sizeof(gateway_cfg_common_t), &config->cfg_common);
    if (ret < 0) {
        checkret |= OPEN_COMMON_ERR;
    }

    // 写入板卡基础配置
    ret = write_configure(DEVICE_TYPE_SWITCH, START_ADDR_BOARD_BASIC,
                         sizeof(gateway_cfg_board_basic_t), &config->cfg_board_basic);
    if (ret < 0) {
        checkret |= OPEN_BASIC_ERR;
    }

    // 写入会议配置（设置语音IP为网络IP）
    gateway_cfg_conf_t conf = config->cfg_conf;
    conf.voice_ip = config->cfg_net.ip;
    ret = write_configure(eTypeSwitch, START_ADDR_CONF,
                         sizeof(gateway_cfg_conf_t), &conf);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_CONF_ERR;
    }

    // 写入对端基站配置
    gateway_cfg_peer_base_t peer = config->cfg_peer_base;
    peer.peer_ip = config->cfg_net.ip;
    peer.peer_voice_ip = config->cfg_net.ip;
    peer.peer_type = eTypePdt;
    peer.peer_voice_port_base = FUNCTION_VOICE_SWITCH_PORT_BASE;
    peer.peer_data_listen_port = CALLCENTER_RECV_PORT;
    peer.peer_net_address = config->cfg_center.center_no;
    ret = write_configure(eTypeSwitch, START_ADDR_CONF_PEER,
                         sizeof(gateway_cfg_peer_base_t), &peer);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_CONF_ERR;
    }

    // 写入呼叫中心配置
    ret = write_configure(eTypeCallcenter, START_ADDR_CALLCENTER,
                         sizeof(gateway_cfg_center_t), &config->cfg_center);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_CENTER_ERR;
    }

    // 写入编解码配置
    ret = write_configure(eTypeSwitch, START_ADDR_VOCODER,
                         sizeof(gateway_cfg_vocoder_t), &config->cfg_vocoder);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_VOCODER_ERR;
    }

    // 写入基站配置
    ret = write_configure(eTypeSwitch, START_ADDR_BASE,
                         sizeof(gateway_cfg_base_t), &config->cfg_base);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_BASE_ERR;
    }

    // 写入频率配置
    ret = write_configure(eTypeSwitch, START_ADDR_FREQ,
                         sizeof(gateway_cfg_freq_t), &config->cfg_freq);
    if (ret < 0) {
        checkret |= GATEWAY_OPEN_BASE_ERR;
    }

    if (checkret == GATEWAY_READ_SUCCESS) {
        checkret = GATEWAY_SAVE_SUCCESS;
    }

    return checkret;
}

/**
 * 设置默认网关配置
 */
void set_default_gateway_config(gateway_config_t *config) {
    if (!config) {
        return;
    }

    // 清零结构体
    memset(config, 0, sizeof(gateway_config_t));

    // 默认网络配置
    strcpy(config->board_net.ethconfig.ip, "***********00");
    strcpy(config->board_net.ethconfig.mask, "*************");
    strcpy(config->board_net.ethconfig.gateway, "***********");
    strcpy(config->board_net.ethconfig.dns, "*******");
    strcpy(config->board_net.ethconfig.mac, "00:11:22:33:44:55");
    config->board_net.wlan_enable_val = 0;

    // 转换为数字格式
    eth_str_to_num(&config->board_net.ethconfig, &config->cfg_net);

    // 默认通用配置
    config->cfg_common.ds = 1;
    config->cfg_common.sw = 1;
    config->cfg_common.conf_num = 4;
    config->cfg_common.normal_num = 16;

    // 默认板卡基础配置
    config->cfg_board_basic.daemon_ip = config->cfg_net.ip;
    config->cfg_board_basic.daemon_port = 2500;
    config->cfg_board_basic.log_ip = config->cfg_net.ip;
    config->cfg_board_basic.log_port = 514;
    config->cfg_board_basic.cfg_ip = config->cfg_net.ip;
    config->cfg_board_basic.cfg_port = 80;
    config->cfg_board_basic.log_level = 3;
    config->cfg_board_basic.log_to_where = 0;
    config->cfg_board_basic.data_listen_port = 2600;
    config->cfg_board_basic.data_send_port = 2601;

    // 默认会议配置
    config->cfg_conf.get_cfg_method = 0;
    config->cfg_conf.network_mode = 0;
    config->cfg_conf.voice_ip = config->cfg_net.ip;
    config->cfg_conf.data_listen_port = 2600;
    config->cfg_conf.vbus_base_port = VOICE_PORT_BASE;
    config->cfg_conf.net_address = 0x000001;
    config->cfg_conf.base_type = 0x10;
    config->cfg_conf.vcan_number = 1;
    config->cfg_conf.buffertime = 5000;
    config->cfg_conf.downtime = 30000;
    config->cfg_conf.resettime = 60;
    config->cfg_conf.peer_base_num = 1;
    config->cfg_conf.spec_function = 0;

    // 默认对端基站配置
    config->cfg_peer_base.peer_ip = config->cfg_net.ip;
    config->cfg_peer_base.peer_voice_ip = config->cfg_net.ip;
    config->cfg_peer_base.peer_type = eTypePdt;
    config->cfg_peer_base.peer_voice_port_base = FUNCTION_VOICE_SWITCH_PORT_BASE;
    config->cfg_peer_base.peer_data_listen_port = CALLCENTER_RECV_PORT;
    config->cfg_peer_base.peer_net_address = 0x000001;

    // 默认呼叫中心配置
    config->cfg_center.center_no = 0x000001;
    config->cfg_center.center_outssi = 0x000001;
    config->cfg_center.center_inssi = 0x000001;
    config->cfg_center.vchan_sum = 10;
    config->cfg_center.center_voice_port = 3000;
    config->cfg_center.listen_agent_port = 2700;
    config->cfg_center.peer_net_type = 0;
    config->cfg_center.send_all_agent_ip = config->cfg_net.ip;
    config->cfg_center.send_to_agent_port = 2600;
    config->cfg_center.inssi_num = 100;
    config->cfg_center.spec_function = 0;

    // 默认编解码配置
    config->cfg_vocoder.amp = 0;
    config->cfg_vocoder.cs = 0;
    config->cfg_vocoder.pcm = 0;
    config->cfg_vocoder.vocoder = 0;

    // 默认基站配置
    config->cfg_base.syscode = 0x0001;

    // 默认频率配置
    for (int i = 0; i < CFG_FREQ_SUM; i++) {
        config->cfg_freq.freq[i] = 0x1C00 + i;
    }
}

// ===================== 工具函数实现 =====================

/**
 * 字符串地址转换为数字格式 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
static void eth_str_to_num(const gateway_net_config_t *pConfig, gateway_cfg_net_t *pNet) {
    if (!pConfig || !pNet) {
        return;
    }

    pNet->ip = inet_addr(pConfig->ip);
    pNet->mask = inet_addr(pConfig->mask);
    pNet->gateway = inet_addr(pConfig->gateway);
    pNet->dns = inet_addr(pConfig->dns);

    // TODO: 转换MAC地址
    memset(pNet->mac, 0, 6);
}

/**
 * 数字地址转换为字符串格式
 */
static void eth_num_to_str(const gateway_cfg_net_t *pNet, gateway_net_config_t *pConfig) {
    if (!pNet || !pConfig) {
        return;
    }

    struct in_addr addr;

    addr.s_addr = pNet->ip;
    strncpy(pConfig->ip, inet_ntoa(addr), LEN_IP_ADDR-1);

    addr.s_addr = pNet->mask;
    strncpy(pConfig->mask, inet_ntoa(addr), LEN_IP_ADDR-1);

    addr.s_addr = pNet->gateway;
    strncpy(pConfig->gateway, inet_ntoa(addr), LEN_IP_ADDR-1);

    addr.s_addr = pNet->dns;
    strncpy(pConfig->dns, inet_ntoa(addr), LEN_IP_ADDR-1);

    // TODO: 转换MAC地址
    strcpy(pConfig->mac, "00:11:22:33:44:55");
}
