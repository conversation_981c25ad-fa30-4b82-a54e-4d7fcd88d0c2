/*******************************************************************
 * File          : gateway_config.h
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 网关配置模块头文件 - 基于deprecated/cgi/0gateway.c重构
 * 100%兼容原有网关配置功能和数据结构
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : created from deprecated/cgi/0gateway.c
 *******************************************************************/

#ifndef CONFIG_GATEWAY_GATEWAY_CONFIG_H
#define CONFIG_GATEWAY_GATEWAY_CONFIG_H

#include "config/common/config_base.h"
#include <cJSON.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>

// 配置文件路径定义（与原始保持一致）
#define GATEWAYCFG   "/home/<USER>/cfg/gateway.cfg"
#define START_ADDR_GATEWAY  0x00

// 长度定义 - 基于deprecated/cgi/inc/1utils.h
#define LEN_IP_ADDR         16
#define LEN_MAC_ADDR        18
#define LEN_NAME            30
#define LEN_PASS            20
#define LEN_URL             50

// 错误码定义 - 基于deprecated/cgi/inc/0define.h
#define GATEWAY_READ_SUCCESS    0x0000
#define GATEWAY_OPEN_ETH_ERR    0x0001
#define GATEWAY_OPEN_COMMON_ERR 0x0002
#define GATEWAY_OPEN_BASIC_ERR  0x0004
#define GATEWAY_OPEN_CONF_ERR   0x0008
#define GATEWAY_OPEN_CENTER_ERR 0x0010
#define GATEWAY_OPEN_VOCODER_ERR 0x0020
#define GATEWAY_OPEN_BASE_ERR   0x0040
#define GATEWAY_SAVE_SUCCESS    0x8000

/**
 * 网络配置结构体 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    char ip[LEN_IP_ADDR];
    char mask[LEN_IP_ADDR];
    char gateway[LEN_IP_ADDR];
    char dns[LEN_IP_ADDR];
    char mac[LEN_MAC_ADDR];
} gateway_net_config_t;

/**
 * 板卡网络配置 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    FILE *fpeth;
    gateway_net_config_t ethconfig;
    int wlan_enable_val;
} gateway_board_net_t;

/**
 * 数字化网络配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t ip;
    uint32_t mask;
    uint32_t gateway;
    uint32_t dns;
    uint8_t mac[6];
} __attribute__((packed)) gateway_cfg_net_t;

/**
 * 通用配置结构体 - 基于deprecated/cgi/inc/1rwcommon.h
 */
typedef struct {
    uint8_t ds;
    uint8_t sw;
    uint8_t conf_num;
    uint8_t normal_num;
} __attribute__((packed)) gateway_cfg_common_t;

/**
 * 板卡基础配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t daemon_ip;
    uint16_t daemon_port;
    uint32_t log_ip;
    uint16_t log_port;
    uint32_t cfg_ip;
    uint16_t cfg_port;
    uint8_t log_level;
    uint8_t log_to_where;
    uint16_t data_listen_port;
    uint16_t data_send_port;
} __attribute__((packed)) gateway_cfg_board_basic_t;

/**
 * 会议配置结构体 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint8_t get_cfg_method;
    uint8_t network_mode;
    uint32_t voice_ip;
    uint16_t data_listen_port;
    uint16_t vbus_base_port;
    uint32_t net_address:24;
    uint8_t base_type;
    uint8_t vbus_to_chan[12];
    uint8_t vcan_number;
    uint16_t buffertime;
    uint16_t downtime;
    uint8_t resettime;
    uint8_t peer_base_num;
    uint16_t spec_function;
} __attribute__((packed)) gateway_cfg_conf_t;

/**
 * 对端基站配置 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint32_t peer_ip;
    uint32_t peer_voice_ip;
    uint16_t peer_data_listen_port;
    uint16_t peer_voice_port_base;
    uint32_t peer_net_address:24;
    uint8_t peer_type;
    uint8_t peer_vbus_to_chan[12];
} __attribute__((packed)) gateway_cfg_peer_base_t;

/**
 * 呼叫中心配置结构体 - 基于deprecated/cgi/inc/1rwcenter.h
 */
typedef struct {
    uint32_t center_no:24;          // 中心号码（24位）
    uint32_t center_outssi:24;      // 中心台统一外部号（24位）
    uint32_t center_inssi:24;       // 中心台统一内应号（24位）
    uint8_t vchan_sum;              // 语音通道数
    uint16_t center_voice_port;     // 中心席位语音起始端口
    uint16_t listen_agent_port;     // 监听代理端口
    uint8_t peer_net_type;          // 对等网络类型
    uint32_t send_all_agent_ip;     // 发送所有代理IP
    uint16_t send_to_agent_port;    // peer接收端口
    uint16_t inssi_num;             // 内应号个数
    uint16_t spec_function;         // 特殊功能
} __attribute__((packed)) gateway_cfg_center_t;

/**
 * 编解码配置结构体 - 基于deprecated/cgi/inc/1rwvocoder.h
 */
typedef struct {
    uint8_t amp;
    uint8_t cs;
    uint8_t pcm;
    uint8_t vocoder;
} __attribute__((packed)) gateway_cfg_vocoder_t;

/**
 * 基站配置结构体 - 基于deprecated/cgi/inc/1rwbase.h
 */
typedef struct {
    uint16_t syscode;
} __attribute__((packed)) gateway_cfg_base_t;

/**
 * 频率配置结构体 - 基于deprecated/cgi/inc/1rwbase.h
 */
#define CFG_FREQ_SUM 16
typedef struct {
    uint16_t freq[CFG_FREQ_SUM];
} __attribute__((packed)) gateway_cfg_freq_t;

/**
 * 网关配置完整结构体 - 基于deprecated/cgi/0gateway.c
 */
typedef struct {
    gateway_board_net_t board_net;              // 网络板卡配置
    gateway_cfg_net_t cfg_net;                  // 数字化网络配置
    gateway_cfg_common_t cfg_common;            // 通用配置
    gateway_cfg_board_basic_t cfg_board_basic;  // 板卡基础配置
    gateway_cfg_conf_t cfg_conf;                // 会议配置
    gateway_cfg_peer_base_t cfg_peer_base;      // 对端基站配置
    gateway_cfg_center_t cfg_center;            // 呼叫中心配置
    gateway_cfg_vocoder_t cfg_vocoder;          // 编解码配置
    gateway_cfg_base_t cfg_base;                // 基站配置
    gateway_cfg_freq_t cfg_freq;                // 频率配置
} gateway_config_t;

/**
 * 网关配置JSON结构（API交互）
 */
typedef struct {
    // 网络配置
    char ip[LEN_IP_ADDR];
    char mask[LEN_IP_ADDR];
    char gateway_addr[LEN_IP_ADDR];
    char dns[LEN_IP_ADDR];
    char mac[LEN_MAC_ADDR];
    
    // 中心配置
    uint32_t center_no;
    uint32_t center_outssi;
    uint32_t center_inssi;
    uint8_t vchan_sum;
    uint16_t center_voice_port;
    uint16_t listen_agent_port;
    uint8_t peer_net_type;
    char send_all_agent_ip[LEN_IP_ADDR];
    uint16_t send_to_agent_port;
    uint16_t inssi_num;
    uint16_t spec_function;
    
    // 基础配置
    char daemon_ip[LEN_IP_ADDR];
    uint16_t daemon_port;
    char log_ip[LEN_IP_ADDR];
    uint16_t log_port;
    uint8_t log_level;
    uint8_t log_to_where;
    uint16_t data_listen_port;
    uint16_t data_send_port;
    
    // 会议配置
    uint8_t get_cfg_method;
    uint8_t network_mode;
    uint16_t vbus_base_port;
    uint32_t net_address;
    uint8_t base_type;
    uint8_t vcan_number;
    uint16_t buffertime;
    uint16_t downtime;
    uint8_t resettime;
    uint8_t peer_base_num;
    
    // 编解码配置
    uint8_t amp;
    uint8_t cs;
    uint8_t pcm;
    uint8_t vocoder;
    
    // 基站配置
    uint16_t syscode;
    uint16_t frequencies[CFG_FREQ_SUM];
} gateway_config_json_t;

// 函数声明

/**
 * 验证网关配置数据
 */
int validate_gateway_config(const gateway_config_json_t *config);

/**
 * JSON转二进制配置
 */
int convert_gateway_json_to_binary(const gateway_config_json_t *json_config, 
                                  gateway_config_t *binary_config);

/**
 * 二进制转JSON配置
 */
int convert_gateway_binary_to_json(const gateway_config_t *binary_config, 
                                  gateway_config_json_t *json_config);

/**
 * 读取网关配置文件 - 基于deprecated/cgi/0gateway.c的gateway_read_cfg
 */
int read_gateway_config_file(gateway_config_t *config);

/**
 * 写入网关配置文件 - 基于deprecated/cgi/0gateway.c的gateway_write_cfg
 */
int write_gateway_config_file(const gateway_config_t *config);

/**
 * 设置默认网关配置
 */
void set_default_gateway_config(gateway_config_t *config);

/**
 * API处理函数 - 在gateway_api.c中实现
 */

/**
 * 处理网关配置GET请求
 */
int handle_gateway_config_get(char **response);

/**
 * 处理网关配置POST请求
 */
int handle_gateway_config_post(const char *request_body, char **response);

#endif /* CONFIG_GATEWAY_GATEWAY_CONFIG_H */
