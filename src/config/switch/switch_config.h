/**
 * @file switch_config.h
 * @brief 交换机配置模块头文件
 * <AUTHOR> Team
 * @date 2024
 */

#ifndef SWITCH_CONFIG_H
#define SWITCH_CONFIG_H

#include <stdint.h>
#include <stdio.h>
#include "config/common/config_base.h"

/**
 * 交换机类型定义 - 基于deprecated/cgi/0define.h
 */
typedef enum {
    SWITCH_TYPE_BASIC = 0x10,    // 基础交换机 (0switch.c)
    SWITCH_TYPE_3G = 0x18,       // 3G交换机 (0switch3g.c)  
    SWITCH_TYPE_PUBLIC = 0x19    // 公网交换机 (0switchpub.c)
} switch_type_t;

/**
 * 交换机错误码 - 基于deprecated/cgi/0define.h
 */
typedef enum {
    SWITCH_READ_SUCCESS = 0,
    SWITCH_SAVE_SUCCESS = 1,
    SWITCH_OPEN_ETH_ERR = (1 << 1),
    SWITCH_OPEN_BASIC_ERR = (1 << 11),
    SWITCH_OPEN_3G_ERR = (1 << 12),
    SWITCH_OPEN_NET_ERR = (1 << 13),
    SWITCH_OPEN_COMMON_ERR = (1 << 14),
    SWITCH_OPEN_CONF_ERR = (1 << 15)
} switch_error_t;

// 长度定义 - 基于deprecated/cgi/inc/1utils.h
#define LEN_IP_ADDR         16
#define LEN_MAC_ADDR        18
#define LEN_NAME            30
#define LEN_PASS            20
#define LEN_URL             50
#define LEN_URL_ADDR        50

/**
 * 网络配置结构体 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    char ip[LEN_IP_ADDR];
    char mask[LEN_IP_ADDR];
    char gateway[LEN_IP_ADDR];
    char dns[LEN_IP_ADDR];
    char mac[LEN_MAC_ADDR];
} switch_net_config_t;

/**
 * 板卡网络配置 - 基于deprecated/cgi/inc/1rwethconfig.h
 */
typedef struct {
    FILE *fpeth;
    switch_net_config_t ethconfig;
    int wlan_enable_val;
} switch_board_net_t;

/**
 * 数字化网络配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t ip;
    uint32_t mask;
    uint32_t gateway;
    uint32_t dns;
    uint8_t mac[6];
} __attribute__((packed)) switch_cfg_net_t;

/**
 * 通用配置结构体 - 基于deprecated/cgi/inc/1rwcommon.h
 */
typedef struct {
    uint8_t ds;
    uint8_t sw;
    uint8_t conf_num;
    uint8_t normal_num;
} __attribute__((packed)) switch_cfg_common_t;

/**
 * 板卡基础配置 - 基于deprecated/cgi/inc/0define.h
 */
typedef struct {
    uint32_t daemon_ip;
    uint16_t daemon_port;
    uint32_t log_ip;
    uint16_t log_port;
    uint32_t cfg_ip;
    uint16_t cfg_port;
    uint8_t log_level;
    uint8_t log_to_where;
    uint16_t data_listen_port;
    uint16_t data_send_port;
} __attribute__((packed)) switch_cfg_board_basic_t;

/**
 * 会议配置结构体 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint32_t work_mode;      // read only
    uint16_t spec_function;
    uint8_t peer_base_num;
    uint32_t voice_ip;
    uint16_t vbus_base_port;
    uint8_t vchan_number;
    uint16_t buffertime;
    uint16_t downtime;
} __attribute__((packed)) switch_cfg_conf_t;

/**
 * 对端基站配置 - 基于deprecated/cgi/inc/1rwconference.h
 */
typedef struct {
    uint32_t peer_ip;
    uint32_t peer_voice_ip;
    uint16_t peer_data_listen_port;
    uint16_t peer_voice_port_base;
    uint32_t peer_net_address:24;
    uint8_t peer_type;
    uint8_t peer_vbus_to_chan[12];  // 偶数
} __attribute__((packed)) switch_cfg_peer_base_t;

/**
 * 网络选择配置 - 基于deprecated/cgi/inc/1rwnetwork.h
 */
typedef struct {
    FILE *fpnetcs;
    int net_type;
    char test_eth1[LEN_URL_ADDR];
    char test_eth2[LEN_URL_ADDR];
    char test_3g1[LEN_URL_ADDR];
    char test_3g2[LEN_URL_ADDR];
} switch_board_netcs_t;

/**
 * WLAN配置结构体 - 基于deprecated/cgi/inc/1rwwlanconfig.h
 */
typedef struct {
    char name[LEN_NAME];         // 热点AP名称长度
    char passwd[LEN_PASS];       // 热点密码长度
} switch_wlan_ap_t;

typedef struct {
    char ip[LEN_IP_ADDR];
    char mask[LEN_IP_ADDR];
    char gateway[LEN_IP_ADDR];
    char dns[LEN_IP_ADDR];
} switch_wlan_static_t;

typedef struct {
    FILE *fpwlan;
    switch_wlan_ap_t ap;
    int dhcp;
    switch_wlan_static_t ipcfg;
    int wpaver;
} switch_board_wlan_t;

/**
 * 3G配置结构体 - 基于deprecated/cgi/inc/1rw3gconfig.h
 */
typedef struct {
    char apn[LEN_NAME];          // apn
    char name[LEN_NAME];         // 用户名
    char passwd[LEN_PASS];       // 密码
    int auth;                    // vpdn auth type 0 ~ None, 1 ~ Pap, 2 ~ Chap, 3 ~ MsChapV2
} switch_vpdn_cfg_t;

typedef struct {
    int vender;             // 提供商 0=3322, 1=花生壳
    char name[LEN_NAME];    // 用户名
    char passwd[LEN_PASS];  // 密码
    char url[LEN_URL];      // 服务器地址
} switch_ddns_cfg_t;

typedef struct {
    FILE *fp3g;
    int mode;       // 3g模式 0=不使用3G拨号，1=WCDMA，2=CDMA，3=TDSCDMA
    int dynip;      // 动态IP获取方式 0=不需要动态IP，1=使用代理专网，2=使用动态域名
    switch_vpdn_cfg_t vpdn;       // 代理专网配置
    switch_ddns_cfg_t ddns;       // 动态域名配置
} switch_board_3g_t;

/**
 * 3G对等配置 - 基于deprecated/cgi/inc/1rw3gconfig.h
 */
typedef struct {
    int rtype;          
    char raddr[50];  // LEN_RADDR
    int lport;
    int rport;
} switch_board_peer_t;

/**
 * 完整的交换机配置结构体
 */
typedef struct {
    switch_type_t type;
    
    // 基础配置（所有交换机类型都需要）
    switch_board_net_t board_net;
    switch_cfg_net_t cfg_net;
    switch_cfg_common_t cfg_common;
    switch_cfg_board_basic_t cfg_board_basic;
    switch_cfg_conf_t cfg_conf;
    switch_cfg_peer_base_t cfg_peer_base;
    switch_board_netcs_t board_netcs;
    
    // 扩展配置（3G和公网交换机需要）
    switch_board_wlan_t board_wlan;
    switch_board_3g_t board_3g;
    
    // 对等配置（仅公网交换机需要）
    switch_board_peer_t board_peer;
} switch_config_t;

/**
 * 交换机配置操作函数
 */

/**
 * 创建交换机配置实例
 * @param type 交换机类型
 * @return switch_config_t* 配置实例指针，失败返回NULL
 */
switch_config_t* switch_config_create(switch_type_t type);

/**
 * 销毁交换机配置实例
 * @param config 配置实例指针
 */
void switch_config_destroy(switch_config_t* config);

/**
 * 读取交换机配置
 * @param config 配置实例指针
 * @return int 错误码，0表示成功
 */
int switch_config_read(switch_config_t* config);

/**
 * 写入交换机配置
 * @param config 配置实例指针
 * @return int 错误码，0表示成功
 */
int switch_config_write(switch_config_t* config);

/**
 * 验证交换机配置
 * @param config 配置实例指针
 * @return int 错误码，0表示成功
 */
int switch_config_validate(switch_config_t* config);

/**
 * 转换配置为JSON格式
 * @param config 配置实例指针
 * @return char* JSON字符串，需要调用者释放
 */
char* switch_config_to_json(const switch_config_t* config);

/**
 * 从JSON解析配置
 * @param json_str JSON字符串
 * @param config 配置实例指针
 * @return int 错误码，0表示成功
 */
int switch_config_from_json(const char* json_str, switch_config_t* config);

/**
 * 设置默认配置值
 * @param config 配置实例指针
 */
void switch_config_set_defaults(switch_config_t* config);

/**
 * 检测交换机类型
 * @return switch_type_t 检测到的交换机类型
 */
// 移除了switch_detect_type函数声明，因为旧项目中不存在此功能

/**
 * 获取交换机类型名称
 * @param type 交换机类型
 * @return const char* 类型名称字符串
 */
const char* switch_get_type_name(switch_type_t type);

// 统一配置操作接口实例
extern const config_operations_t switch_config_ops;

// API函数声明
struct MHD_Connection; // 前向声明

/**
 * 处理交换机配置获取请求
 * GET /api/v1/config/device/switch
 */
int switch_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data);

/**
 * 处理交换机配置保存请求
 * POST /api/v1/config/device/switch
 */
int switch_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data);

#endif /* SWITCH_CONFIG_H */