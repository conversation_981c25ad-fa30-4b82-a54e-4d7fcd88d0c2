/*******************************************************************
 * File          : switch_api.c
 * Author        : WebCfg Refactor Team
 * Created       : 2025-01-01
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 交换机配置API处理模块
 * 基于deprecated/cgi/0switch.c实现，确保100%兼容
 *------------------------------------------------------------------
 * Modification history :
 * 2025-01-01 : 创建交换机API模块，统一代码组织结构
 *******************************************************************/

#include "switch/switch_config.h"
#include "core/response.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <cJSON.h>

/**
 * 处理交换机配置获取请求
 * GET /api/v1/config/device/switch
 */
int switch_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    switch_config_binary_t binary_config;
    cJSON *json_response = NULL;
    int ret;
    
    // 使用默认交换机类型（保持与旧项目一致，不进行硬件检测）
    
    // 读取交换机配置
    ret = read_switch_config_file(&binary_config);
    if (ret != 0) {
        return response_send_error(connection, 500, "Failed to read switch configuration");
    }
    
    // 转换为JSON格式
    switch_config_json_t json_config;
    ret = convert_switch_binary_to_json(&binary_config, &json_config);
    if (ret != 0) {
        return response_send_error(connection, 500, "Failed to convert switch configuration");
    }
    
    // 创建JSON响应
    json_response = cJSON_CreateObject();
    if (!json_response) {
        return response_send_error(connection, 500, "Failed to create JSON response");
    }
    
    // 添加配置数据到JSON
    cJSON_AddNumberToObject(json_response, "switch_id", json_config.switch_id);
    cJSON_AddNumberToObject(json_response, "board_type", json_config.board_type);
    cJSON_AddStringToObject(json_response, "switch_name", json_config.switch_name);
    cJSON_AddStringToObject(json_response, "firmware_version", json_config.firmware_version);
    
    // 网络配置
    cJSON *network = cJSON_CreateObject();
    cJSON_AddStringToObject(network, "ip", json_config.ip);
    cJSON_AddStringToObject(network, "mask", json_config.mask);
    cJSON_AddStringToObject(network, "gateway", json_config.gateway);
    cJSON_AddStringToObject(network, "dns", json_config.dns);
    cJSON_AddStringToObject(network, "mac", json_config.mac);
    cJSON_AddItemToObject(json_response, "network", network);
    
    // 交换机配置
    cJSON *switch_config = cJSON_CreateObject();
    cJSON_AddNumberToObject(switch_config, "center_port", json_config.center_port);
    cJSON_AddNumberToObject(switch_config, "local_port", json_config.local_port);
    cJSON_AddNumberToObject(switch_config, "channel_count", json_config.channel_count);
    cJSON_AddNumberToObject(switch_config, "switch_mode", json_config.switch_mode);
    cJSON_AddNumberToObject(switch_config, "priority_level", json_config.priority_level);
    cJSON_AddNumberToObject(switch_config, "timeout_interval", json_config.timeout_interval);
    cJSON_AddItemToObject(json_response, "switch_config", switch_config);
    
    // 设备信息
    cJSON_AddNumberToObject(json_response, "switch_type", switch_type);
    cJSON_AddStringToObject(json_response, "switch_type_name", get_switch_type_string(switch_type));
    
    // 发送响应
    ret = response_send_success(connection, json_response);
    cJSON_Delete(json_response);
    
    return ret;
}

/**
 * 处理交换机配置保存请求
 * POST /api/v1/config/device/switch
 */
int switch_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    cJSON *json_request = NULL;
    switch_config_json_t json_config;
    switch_config_binary_t binary_config;
    int ret;
    
    // 解析JSON请求
    json_request = cJSON_Parse(request_data);
    if (!json_request) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    // 清空配置结构
    memset(&json_config, 0, sizeof(json_config));
    
    // 解析基本配置
    cJSON *item;
    if ((item = cJSON_GetObjectItem(json_request, "switch_id"))) {
        json_config.switch_id = (uint32_t)item->valuedouble;
    }
    if ((item = cJSON_GetObjectItem(json_request, "board_type"))) {
        json_config.board_type = (board_type_t)item->valueint;
    }
    if ((item = cJSON_GetObjectItem(json_request, "switch_name"))) {
        strncpy(json_config.switch_name, item->valuestring, sizeof(json_config.switch_name) - 1);
    }
    if ((item = cJSON_GetObjectItem(json_request, "firmware_version"))) {
        strncpy(json_config.firmware_version, item->valuestring, sizeof(json_config.firmware_version) - 1);
    }
    
    // 解析网络配置
    cJSON *network = cJSON_GetObjectItem(json_request, "network");
    if (network) {
        if ((item = cJSON_GetObjectItem(network, "ip"))) {
            strncpy(json_config.ip, item->valuestring, sizeof(json_config.ip) - 1);
        }
        if ((item = cJSON_GetObjectItem(network, "mask"))) {
            strncpy(json_config.mask, item->valuestring, sizeof(json_config.mask) - 1);
        }
        if ((item = cJSON_GetObjectItem(network, "gateway"))) {
            strncpy(json_config.gateway, item->valuestring, sizeof(json_config.gateway) - 1);
        }
        if ((item = cJSON_GetObjectItem(network, "dns"))) {
            strncpy(json_config.dns, item->valuestring, sizeof(json_config.dns) - 1);
        }
        if ((item = cJSON_GetObjectItem(network, "mac"))) {
            strncpy(json_config.mac, item->valuestring, sizeof(json_config.mac) - 1);
        }
    }
    
    // 解析交换机配置
    cJSON *switch_config_obj = cJSON_GetObjectItem(json_request, "switch_config");
    if (switch_config_obj) {
        if ((item = cJSON_GetObjectItem(switch_config_obj, "center_port"))) {
            json_config.center_port = (uint16_t)item->valueint;
        }
        if ((item = cJSON_GetObjectItem(switch_config_obj, "local_port"))) {
            json_config.local_port = (uint16_t)item->valueint;
        }
        if ((item = cJSON_GetObjectItem(switch_config_obj, "channel_count"))) {
            json_config.channel_count = (uint8_t)item->valueint;
        }
        if ((item = cJSON_GetObjectItem(switch_config_obj, "switch_mode"))) {
            json_config.switch_mode = (uint8_t)item->valueint;
        }
        if ((item = cJSON_GetObjectItem(switch_config_obj, "priority_level"))) {
            json_config.priority_level = (uint8_t)item->valueint;
        }
        if ((item = cJSON_GetObjectItem(switch_config_obj, "timeout_interval"))) {
            json_config.timeout_interval = (uint16_t)item->valueint;
        }
    }
    
    // 验证配置
    ret = validate_switch_config(&json_config);
    if (ret != 0) {
        cJSON_Delete(json_request);
        return response_send_error(connection, 400, "Invalid switch configuration");
    }
    
    // 转换为二进制格式
    ret = convert_switch_json_to_binary(&json_config, &binary_config);
    if (ret != 0) {
        cJSON_Delete(json_request);
        return response_send_error(connection, 500, "Failed to convert switch configuration");
    }
    
    // 保存配置
    ret = write_switch_config_file(&binary_config);
    if (ret != 0) {
        cJSON_Delete(json_request);
        return response_send_error(connection, 500, "Failed to save switch configuration");
    }
    
    // 创建成功响应
    cJSON *json_response = cJSON_CreateObject();
    cJSON_AddStringToObject(json_response, "message", "Switch configuration saved successfully");
    cJSON_AddNumberToObject(json_response, "switch_id", json_config.switch_id);
    
    ret = response_send_success(connection, json_response);
    
    // 清理资源
    cJSON_Delete(json_request);
    cJSON_Delete(json_response);
    
    return ret;
}
