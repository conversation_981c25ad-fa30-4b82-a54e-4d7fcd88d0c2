#include "utils/file/file_utils.h"
#include "utils/file/config_file_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>

// 基于偏移量的文件读取操作
int file_read_at_offset(const char *path, long offset, void *data, size_t size) {
    if (!path || !data || size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    FILE *file = fopen(path, "rb");
    if (!file) {
        if (errno == ENOENT) {
            return FILE_OPERATION_NOT_FOUND;
        } else if (errno == EACCES) {
            return FILE_OPERATION_PERMISSION;
        }
        return FILE_OPERATION_ERROR;
    }

    if (fseek(file, offset, SEEK_SET) != 0) {
        fclose(file);
        return FILE_OPERATION_ERROR;
    }

    size_t bytes_read = fread(data, 1, size, file);
    fclose(file);

    if (bytes_read != size) {
        return FILE_OPERATION_ERROR;
    }

    return FILE_OPERATION_SUCCESS;
}

// 基于偏移量的文件写入操作
int file_write_at_offset(const char *path, long offset, const void *data, size_t size) {
    if (!path || !data || size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    FILE *file = fopen(path, "r+b");
    if (!file) {
        // 文件不存在，创建新文件
        file = fopen(path, "wb");
        if (!file) {
            if (errno == EACCES) {
                return FILE_OPERATION_PERMISSION;
            } else if (errno == ENOSPC) {
                return FILE_OPERATION_NO_SPACE;
            }
            return FILE_OPERATION_ERROR;
        }
    }

    if (fseek(file, offset, SEEK_SET) != 0) {
        fclose(file);
        return FILE_OPERATION_ERROR;
    }

    size_t bytes_written = fwrite(data, 1, size, file);
    int sync_result = fflush(file);
    fclose(file);

    if (bytes_written != size || sync_result != 0) {
        return FILE_OPERATION_ERROR;
    }

    return FILE_OPERATION_SUCCESS;
}

// 完整文件读取操作
int file_read_full(const char *path, void *data, size_t max_size, size_t *actual_size) {
    if (!path || !data || max_size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    FILE *file = fopen(path, "rb");
    if (!file) {
        if (errno == ENOENT) {
            return FILE_OPERATION_NOT_FOUND;
        } else if (errno == EACCES) {
            return FILE_OPERATION_PERMISSION;
        }
        return FILE_OPERATION_ERROR;
    }

    size_t bytes_read = fread(data, 1, max_size, file);
    fclose(file);

    if (actual_size) {
        *actual_size = bytes_read;
    }

    return FILE_OPERATION_SUCCESS;
}

// 完整文件写入操作
int file_write_full(const char *path, const void *data, size_t size) {
    if (!path || !data || size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    // 确保目录存在
    if (config_ensure_directory_exists(path) != 0) {
        return FILE_OPERATION_ERROR;
    }

    FILE *file = fopen(path, "wb");
    if (!file) {
        if (errno == EACCES) {
            return FILE_OPERATION_PERMISSION;
        } else if (errno == ENOSPC) {
            return FILE_OPERATION_NO_SPACE;
        }
        return FILE_OPERATION_ERROR;
    }

    size_t bytes_written = fwrite(data, 1, size, file);
    int sync_result = fflush(file);
    fclose(file);

    if (bytes_written != size || sync_result != 0) {
        return FILE_OPERATION_ERROR;
    }

    return FILE_OPERATION_SUCCESS;
}

// 原子性文件写入操作
int file_atomic_write(const char *path, const void *data, size_t size) {
    if (!path || !data || size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    // 创建临时文件
    char temp_path[512];
    if (snprintf(temp_path, sizeof(temp_path), "%s.tmp", path) >= (int)sizeof(temp_path)) {
        return FILE_OPERATION_ERROR;
    }

    // 写入临时文件
    int result = file_write_full(temp_path, data, size);
    if (result != FILE_OPERATION_SUCCESS) {
        unlink(temp_path); // 清理临时文件
        return result;
    }

    // 原子性替换
    if (rename(temp_path, path) != 0) {
        unlink(temp_path); // 清理临时文件
        return FILE_OPERATION_ERROR;
    }

    return FILE_OPERATION_SUCCESS;
}

// 备份并写入文件
int file_backup_and_write(const char *path, const void *data, size_t size) {
    if (!path || !data || size == 0) {
        return FILE_OPERATION_INVALID_ARG;
    }

    // 如果原文件存在，先创建备份
    if (file_exists(path)) {
        if (file_create_backup(path, ".bak") != FILE_OPERATION_SUCCESS) {
            return FILE_OPERATION_ERROR;
        }
    }

    // 写入新数据
    return file_atomic_write(path, data, size);
}

// 检查文件是否存在
int file_exists(const char *path) {
    if (!path) {
        return 0;
    }
    return (access(path, F_OK) == 0) ? 1 : 0;
}

// 获取文件大小
long file_get_size(const char *path) {
    if (!path) {
        return -1;
    }

    struct stat st;
    if (stat(path, &st) == 0) {
        return st.st_size;
    }
    return -1;
}

// 检查文件是否可读
int file_is_readable(const char *path) {
    if (!path) {
        return 0;
    }
    return (access(path, R_OK) == 0) ? 1 : 0;
}

// 检查文件是否可写
int file_is_writable(const char *path) {
    if (!path) {
        return 0;
    }
    return (access(path, W_OK) == 0) ? 1 : 0;
}

// 创建文件备份
int file_create_backup(const char *path, const char *backup_suffix) {
    if (!path || !backup_suffix) {
        return FILE_OPERATION_INVALID_ARG;
    }

    if (!file_exists(path)) {
        return FILE_OPERATION_NOT_FOUND;
    }

    char backup_path[512];
    if (snprintf(backup_path, sizeof(backup_path), "%s%s", path, backup_suffix) >= (int)sizeof(backup_path)) {
        return FILE_OPERATION_ERROR;
    }

    // 读取原文件
    long file_size = file_get_size(path);
    if (file_size < 0) {
        return FILE_OPERATION_ERROR;
    }

    char *buffer = malloc(file_size);
    if (!buffer) {
        return FILE_OPERATION_ERROR;
    }

    size_t actual_size;
    int result = file_read_full(path, buffer, file_size, &actual_size);
    if (result != FILE_OPERATION_SUCCESS) {
        free(buffer);
        return result;
    }

    // 写入备份文件
    result = file_write_full(backup_path, buffer, actual_size);
    free(buffer);

    return result;
}

// 从备份恢复文件
int file_restore_from_backup(const char *path, const char *backup_suffix) {
    if (!path || !backup_suffix) {
        return FILE_OPERATION_INVALID_ARG;
    }

    char backup_path[512];
    if (snprintf(backup_path, sizeof(backup_path), "%s%s", path, backup_suffix) >= (int)sizeof(backup_path)) {
        return FILE_OPERATION_ERROR;
    }

    if (!file_exists(backup_path)) {
        return FILE_OPERATION_NOT_FOUND;
    }

    // 读取备份文件
    long file_size = file_get_size(backup_path);
    if (file_size < 0) {
        return FILE_OPERATION_ERROR;
    }

    char *buffer = malloc(file_size);
    if (!buffer) {
        return FILE_OPERATION_ERROR;
    }

    size_t actual_size;
    int result = file_read_full(backup_path, buffer, file_size, &actual_size);
    if (result != FILE_OPERATION_SUCCESS) {
        free(buffer);
        return result;
    }

    // 写入原文件
    result = file_atomic_write(path, buffer, actual_size);
    free(buffer);

    return result;
}

// 旧接口兼容性实现
int read_file_with_offset(const char *path, long offset, void *data, size_t size) {
    int result = file_read_at_offset(path, offset, data, size);
    return (result == FILE_OPERATION_SUCCESS) ? 0 : -1;
}

int write_file_with_offset(const char *path, long offset, const void *data, size_t size) {
    int result = file_write_at_offset(path, offset, data, size);
    return (result == FILE_OPERATION_SUCCESS) ? 0 : -1;
}