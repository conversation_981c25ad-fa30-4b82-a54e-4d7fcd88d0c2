#include "utils/network_utils.h"
#include <arpa/inet.h>
#include <string.h>

int ip_str_to_num(const char *ip_str, uint32_t *ip_num) {
    if (!ip_str || !ip_num) {
        return -1;
    }
    
    struct in_addr addr;
    if (inet_pton(AF_INET, ip_str, &addr) != 1) {
        return -1;
    }
    
    *ip_num = addr.s_addr;
    return 0;
}

int ip_num_to_str(uint32_t ip_num, char *ip_str, size_t str_size) {
    if (!ip_str || str_size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_num;
    
    if (!inet_ntop(AF_INET, &addr, ip_str, str_size)) {
        return -1;
    }
    
    return 0;
}