#include "utils/file/config_file_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include <libgen.h>

// 配置文件映射表 - 与deprecated模块100%一致
static const config_file_mapping_t file_mappings[] = {
    // 网络配置文件
    {CONFIG_TYPE_NETWORK,   "/etc/eth0-setting",            ".bak", 0644},
    {CONFIG_TYPE_NETWORK_SETTING, "/etc/network-setting",   ".bak", 0644},
    {CONFIG_TYPE_WLAN,      "/etc/wlan0-setting",           ".bak", 0644},
    {CONFIG_TYPE_3G,        "/etc/3g-setting",              ".bak", 0644},
    
    // 设备配置文件 (与deprecated中定义完全一致)
    {CONFIG_TYPE_CENTER,    "/home/<USER>/cfg/callcenter.cfg", ".bak", 0644},
    {CONFIG_TYPE_GATEWAY,   "/home/<USER>/cfg/gateway.cfg",    ".bak", 0644},
    {CONFIG_TYPE_STATION,   "/home/<USER>/cfg/sci.cfg",        ".bak", 0644},
    {CONFIG_TYPE_SWITCH,    "/home/<USER>/cfg/common.cfg",     ".bak", 0644}, // switch使用common.cfg
    {CONFIG_TYPE_RECORDER,  "/home/<USER>/cfg/record.cfg",     ".bak", 0644},
    
    // 系统配置文件
    {CONFIG_TYPE_COMMON,    "/home/<USER>/cfg/common.cfg",     ".bak", 0644},
    {CONFIG_TYPE_BOARD,     "/home/<USER>/cfg/board.cfg",      ".bak", 0644},
    {CONFIG_TYPE_CONFERENCE, "/home/<USER>/cfg/conferece.cfg", ".bak", 0644}, // 注意：保持原有拼写错误以确保兼容性
    {CONFIG_TYPE_VOCODER,   "/home/<USER>/cfg/vocoder.cfg",    ".bak", 0644},
    {CONFIG_TYPE_BASE,      "/home/<USER>/cfg/base.cfg",       ".bak", 0644},
    {CONFIG_TYPE_LINKSWITCH, "/home/<USER>/cfg/linkswitch.cfg", ".bak", 0644},
    {CONFIG_TYPE_MPTLINK,   "/home/<USER>/cfg/mptlink.cfg",    ".bak", 0644},
    
    // 版本文件
    {CONFIG_TYPE_VERSION,   "/home/<USER>/cfg/cfgver.cfg",     ".bak", 0644},
    
    // 日志文件 (为系统模块使用)
    {CONFIG_TYPE_LOG_STARTUP, "/tmp/startipsw.log",          "", 0644},
    {CONFIG_TYPE_LOG_NETWORK, "/tmp/netconfig.log",          "", 0644},
    {CONFIG_TYPE_LOG_WLAN,    "/tmp/wlanconfig.log",         "", 0644},
    {CONFIG_TYPE_LOG_PPP,     "/tmp/pppconfig.log",          "", 0644},
};

static const size_t file_mappings_count = sizeof(file_mappings) / sizeof(file_mappings[0]);

const char* config_get_file_path(config_type_t type) {
    for (size_t i = 0; i < file_mappings_count; i++) {
        if (file_mappings[i].type == type) {
            return file_mappings[i].file_path;
        }
    }
    return NULL;
}

const char* config_get_backup_suffix(config_type_t type) {
    for (size_t i = 0; i < file_mappings_count; i++) {
        if (file_mappings[i].type == type) {
            return file_mappings[i].backup_suffix;
        }
    }
    return ".bak";
}

int config_get_required_permissions(config_type_t type) {
    for (size_t i = 0; i < file_mappings_count; i++) {
        if (file_mappings[i].type == type) {
            return file_mappings[i].required_permissions;
        }
    }
    return 0644;
}

int config_ensure_directory_exists(const char *file_path) {
    if (!file_path) {
        return -1;
    }
    
    char *path_copy = strdup(file_path);
    if (!path_copy) {
        return -1;
    }
    
    char *dir_path = dirname(path_copy);
    
    // 检查目录是否存在
    struct stat st;
    if (stat(dir_path, &st) == 0) {
        if (S_ISDIR(st.st_mode)) {
            free(path_copy);
            return 0; // 目录已存在
        } else {
            free(path_copy);
            return -1; // 路径存在但不是目录
        }
    }
    
    // 创建目录
    if (mkdir(dir_path, 0755) == 0) {
        free(path_copy);
        return 0;
    }
    
    // 如果失败可能是因为父目录不存在，递归创建
    if (errno == ENOENT) {
        // 递归创建父目录
        char *parent_copy = strdup(dir_path);
        if (parent_copy) {
            char *parent_dir = dirname(parent_copy);
            if (strcmp(parent_dir, dir_path) != 0) {
                config_ensure_directory_exists(parent_dir);
                mkdir(dir_path, 0755);
            }
            free(parent_copy);
        }
    }
    
    free(path_copy);
    return (access(dir_path, W_OK) == 0) ? 0 : -1;
}

int config_file_validate_access(const char *file_path) {
    if (!file_path) {
        return -1;
    }
    
    // 确保目录存在
    if (config_ensure_directory_exists(file_path) != 0) {
        return -1;
    }
    
    // 检查文件是否存在
    if (access(file_path, F_OK) == 0) {
        // 文件存在，检查读写权限
        if (access(file_path, R_OK | W_OK) == 0) {
            return 0;
        } else {
            return -1; // 权限不足
        }
    }
    
    // 文件不存在，检查目录写权限
    char *path_copy = strdup(file_path);
    if (!path_copy) {
        return -1;
    }
    
    char *dir_path = dirname(path_copy);
    int result = access(dir_path, W_OK);
    free(path_copy);
    
    return result;
}

int config_get_backup_path(config_type_t type, char *backup_path, size_t size) {
    const char *file_path = config_get_file_path(type);
    const char *suffix = config_get_backup_suffix(type);
    
    if (!file_path || !suffix || !backup_path || size == 0) {
        return -1;
    }
    
    if (strlen(suffix) == 0) {
        // 没有备份文件
        return -1;
    }
    
    if (snprintf(backup_path, size, "%s%s", file_path, suffix) >= (int)size) {
        return -1; // 缓冲区不足
    }
    
    return 0;
}

const config_file_mapping_t* config_get_all_mappings(size_t *count) {
    if (count) {
        *count = file_mappings_count;
    }
    return file_mappings;
} 