#include "utils/conversion/data_converter.h"
#include "utils/conversion/ip_converter.h"
#include "utils/validation/common_validator.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include <errno.h>

// 24位ID处理工具
uint32_t data_get_24bit_value(uint32_t raw_value) {
    return raw_value & 0xFFFFFF;
}

int data_validate_and_convert_24bit(const cJSON *json, const char *field, uint32_t *output) {
    if (!json || !field || !output) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    uint32_t raw_value;
    if (json_get_uint32_safe(json, field, &raw_value) != CONVERTER_SUCCESS) {
        return CONVERTER_ERROR_NOT_FOUND;
    }
    
    // 验证24位ID范围
    if (validate_24bit_id(raw_value) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *output = data_get_24bit_value(raw_value);
    return CONVERTER_SUCCESS;
}

// IP地址转换 (安全接口)
uint32_t ip_str_to_uint32_safe(const char *ip_str) {
    if (!ip_str) {
        return INADDR_NONE;
    }
    
    // 首先验证IP地址格式
    if (ip_validate_address(ip_str) != VALIDATION_SUCCESS) {
        return INADDR_NONE;
    }
    
    return ip_str_to_uint32(ip_str);
}

int ip_uint32_to_str_safe(uint32_t ip_num, char *buffer, size_t buffer_size) {
    if (!buffer || buffer_size < INET_ADDRSTRLEN) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    if (ip_uint32_to_str(ip_num, buffer, buffer_size) != 0) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

uint32_t ip_str_to_uint32_host_safe(const char *ip_str) {
    uint32_t ip_net = ip_str_to_uint32_safe(ip_str);
    if (ip_net == INADDR_NONE) {
        return 0;
    }
    return ntohl(ip_net);
}

int ip_uint32_host_to_str_safe(uint32_t ip_host, char *buffer, size_t buffer_size) {
    if (!buffer || buffer_size < INET_ADDRSTRLEN) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    uint32_t ip_net = htonl(ip_host);
    return ip_uint32_to_str_safe(ip_net, buffer, buffer_size);
}

// JSON字段安全提取
int json_get_uint32_safe(const cJSON *json, const char *field, uint32_t *value) {
    if (!json || !field || !value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item) {
        return CONVERTER_ERROR_NOT_FOUND;
    }
    
    if (!cJSON_IsNumber(item)) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT32_MAX) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *value = (uint32_t)dval;
    return CONVERTER_SUCCESS;
}

int json_get_uint16_safe(const cJSON *json, const char *field, uint16_t *value) {
    if (!json || !field || !value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item) {
        return CONVERTER_ERROR_NOT_FOUND;
    }
    
    if (!cJSON_IsNumber(item)) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT16_MAX) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *value = (uint16_t)dval;
    return CONVERTER_SUCCESS;
}

int json_get_uint8_safe(const cJSON *json, const char *field, uint8_t *value) {
    if (!json || !field || !value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item) {
        return CONVERTER_ERROR_NOT_FOUND;
    }
    
    if (!cJSON_IsNumber(item)) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT8_MAX) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *value = (uint8_t)dval;
    return CONVERTER_SUCCESS;
}

int json_get_string_safe(const cJSON *json, const char *field, char *buffer, size_t size) {
    if (!json || !field || !buffer || size == 0) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item) {
        return CONVERTER_ERROR_NOT_FOUND;
    }
    
    if (!cJSON_IsString(item)) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    const char *str = cJSON_GetStringValue(item);
    if (!str) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    if (strlen(str) >= size) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    strncpy(buffer, str, size - 1);
    buffer[size - 1] = '\0';
    
    return CONVERTER_SUCCESS;
}

int json_get_ip_safe(const cJSON *json, const char *field, uint32_t *ip_value) {
    if (!json || !field || !ip_value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    char ip_str[INET_ADDRSTRLEN];
    int result = json_get_string_safe(json, field, ip_str, sizeof(ip_str));
    if (result != CONVERTER_SUCCESS) {
        return result;
    }
    
    uint32_t ip = ip_str_to_uint32_safe(ip_str);
    if (ip == INADDR_NONE) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    *ip_value = ip;
    return CONVERTER_SUCCESS;
}

// 24位ID的JSON处理
int json_get_24bit_id_safe(const cJSON *json, const char *field, uint32_t *id_value) {
    return data_validate_and_convert_24bit(json, field, id_value);
}

int json_set_24bit_id_safe(cJSON *json, const char *field, uint32_t id_value) {
    if (!json || !field) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    // 验证ID范围
    if (validate_24bit_id(id_value) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    uint32_t clean_id = data_get_24bit_value(id_value);
    cJSON *item = cJSON_CreateNumber(clean_id);
    if (!item) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    if (!cJSON_AddItemToObject(json, field, item)) {
        cJSON_Delete(item);
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

// IP地址的JSON处理
int json_get_ip_string_safe(const cJSON *json, const char *field, char *ip_buffer, size_t buffer_size) {
    if (!json || !field || !ip_buffer || buffer_size < INET_ADDRSTRLEN) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    int result = json_get_string_safe(json, field, ip_buffer, buffer_size);
    if (result != CONVERTER_SUCCESS) {
        return result;
    }
    
    // 验证IP地址格式
    if (ip_validate_address(ip_buffer) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

int json_set_ip_from_uint32_safe(cJSON *json, const char *field, uint32_t ip_value) {
    if (!json || !field) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    char ip_str[INET_ADDRSTRLEN];
    if (ip_uint32_to_str_safe(ip_value, ip_str, sizeof(ip_str)) != CONVERTER_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    cJSON *item = cJSON_CreateString(ip_str);
    if (!item) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    if (!cJSON_AddItemToObject(json, field, item)) {
        cJSON_Delete(item);
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

int json_set_ip_from_string_safe(cJSON *json, const char *field, const char *ip_str) {
    if (!json || !field || !ip_str) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    // 验证IP地址格式
    if (ip_validate_address(ip_str) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    cJSON *item = cJSON_CreateString(ip_str);
    if (!item) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    if (!cJSON_AddItemToObject(json, field, item)) {
        cJSON_Delete(item);
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

// 数据类型转换工具
int convert_hex_string_to_uint16(const char *hex_str, uint16_t *value) {
    if (!hex_str || !value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    char *endptr;
    unsigned long long_val = strtoul(hex_str, &endptr, 16);
    
    if (*endptr != '\0' || long_val > UINT16_MAX) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    *value = (uint16_t)long_val;
    return CONVERTER_SUCCESS;
}

int convert_uint16_to_hex_string(uint16_t value, char *hex_str, size_t size) {
    if (!hex_str || size < 5) { // "FFFF\0"
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    if (snprintf(hex_str, size, "%04X", value) >= (int)size) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    return CONVERTER_SUCCESS;
}

int convert_hex_string_to_uint32(const char *hex_str, uint32_t *value) {
    if (!hex_str || !value) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    char *endptr;
    unsigned long long_val = strtoul(hex_str, &endptr, 16);
    
    if (*endptr != '\0' || long_val > UINT32_MAX) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    *value = (uint32_t)long_val;
    return CONVERTER_SUCCESS;
}

int convert_uint32_to_hex_string(uint32_t value, char *hex_str, size_t size) {
    if (!hex_str || size < 9) { // "FFFFFFFF\0"
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    if (snprintf(hex_str, size, "%08X", value) >= (int)size) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    return CONVERTER_SUCCESS;
}

// MAC地址转换
int convert_mac_string_to_bytes(const char *mac_str, uint8_t mac[6]) {
    if (!mac_str || !mac) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    // 使用现有的MAC验证函数
    if (validate_mac_string(mac_str) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    // 解析MAC地址 (支持 : 和 - 分隔符)
    int parsed = sscanf(mac_str, "%2hhx:%2hhx:%2hhx:%2hhx:%2hhx:%2hhx",
                       &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]);
    
    if (parsed != 6) {
        parsed = sscanf(mac_str, "%2hhx-%2hhx-%2hhx-%2hhx-%2hhx-%2hhx",
                       &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]);
    }
    
    if (parsed != 6) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    return CONVERTER_SUCCESS;
}

int convert_mac_bytes_to_string(const uint8_t mac[6], char *mac_str, size_t size) {
    if (!mac || !mac_str || size < 18) { // "XX:XX:XX:XX:XX:XX\0"
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    if (validate_mac_bytes(mac) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_FORMAT;
    }
    
    if (snprintf(mac_str, size, "%02X:%02X:%02X:%02X:%02X:%02X",
                mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]) >= (int)size) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    return CONVERTER_SUCCESS;
}

// 端口号验证和转换
int convert_and_validate_port(const cJSON *json, const char *field, uint16_t *port) {
    if (!json || !field || !port) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    uint16_t port_val;
    int result = json_get_uint16_safe(json, field, &port_val);
    if (result != CONVERTER_SUCCESS) {
        return result;
    }
    
    if (validate_port_number(port_val) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *port = port_val;
    return CONVERTER_SUCCESS;
}

int convert_and_validate_voice_port(const cJSON *json, const char *field, uint16_t *port) {
    if (!json || !field || !port) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    uint16_t port_val;
    int result = json_get_uint16_safe(json, field, &port_val);
    if (result != CONVERTER_SUCCESS) {
        return result;
    }
    
    if (validate_voice_port(port_val) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *port = port_val;
    return CONVERTER_SUCCESS;
}

int convert_and_validate_data_port(const cJSON *json, const char *field, uint16_t *port) {
    if (!json || !field || !port) {
        return CONVERTER_ERROR_INVALID_ARG;
    }
    
    uint16_t port_val;
    int result = json_get_uint16_safe(json, field, &port_val);
    if (result != CONVERTER_SUCCESS) {
        return result;
    }
    
    if (validate_data_port(port_val) != VALIDATION_SUCCESS) {
        return CONVERTER_ERROR_OVERFLOW;
    }
    
    *port = port_val;
    return CONVERTER_SUCCESS;
} 