#include "utils/validation/common_validator.h"
#include "utils/validation/ip_validator.h"
#include <string.h>
#include <arpa/inet.h>

// 前向声明MAC验证函数 (避免包含冲突)
extern int validate_mac_address(const char *mac_str);

// 24位ID验证 (基于deprecated模块：0x000001-0xFFFFFE)
int validate_24bit_id(uint32_t id) {
    // 24位ID有效范围：0x000001 到 0xFFFFFE (排除0x000000和0xFFFFFF)
    if (id == 0 || id > 0xFFFFFE) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

uint32_t get_24bit_value(uint32_t raw_value) {
    // 确保只保留低24位
    return raw_value & 0xFFFFFF;
}

int validate_center_id(uint32_t center_id) {
    return validate_24bit_id(center_id);
}

int validate_gateway_id(uint32_t gateway_id) {
    return validate_24bit_id(gateway_id);
}

int validate_station_id(uint32_t station_id) {
    return validate_24bit_id(station_id);
}

// 端口号验证 (基于deprecated模块：一般端口1024-65535)
int validate_port_number(uint16_t port) {
    // 避免使用系统保留端口 (0-1023)
    if (port < 1024) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_voice_port(uint16_t port) {
    // 语音端口通常在7000以上 (基于deprecated中VOICE_PORT_BASE = 7000)
    if (port < 7000) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_data_port(uint16_t port) {
    // 数据端口通常在2000以上 (基于deprecated中的各种端口定义)
    if (port < 2000) {
        return VALIDATION_ERROR_PORT_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_listen_port(uint16_t port) {
    return validate_port_number(port);
}

// 字符串验证
int validate_string_length(const char *str, size_t min_len, size_t max_len) {
    if (!str) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    
    size_t len = strlen(str);
    if (len < min_len || len > max_len) {
        return VALIDATION_ERROR_LENGTH_INVALID;
    }
    
    return VALIDATION_SUCCESS;
}

int validate_string_not_empty(const char *str) {
    if (!str || strlen(str) == 0) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    return VALIDATION_SUCCESS;
}

// IP地址验证 (统一接口，兼容deprecated模块)
int ip_validate_address(const char *ip_str) {
    return validate_ip_address(ip_str) ? VALIDATION_SUCCESS : VALIDATION_ERROR_IP_INVALID;
}

int ip_validate_netmask(const char *mask_str) {
    return validate_subnet_mask(mask_str) ? VALIDATION_SUCCESS : VALIDATION_ERROR_IP_INVALID;
}

int ip_validate_range(const char *ip_str, const char *min_ip, const char *max_ip) {
    return validate_ip_range(ip_str, min_ip, max_ip) ? VALIDATION_SUCCESS : VALIDATION_ERROR_RANGE_INVALID;
}

int ip_validate_in_subnet(const char *ip, const char *subnet, const char *mask) {
    return validate_ip_in_subnet(ip, subnet, mask) ? VALIDATION_SUCCESS : VALIDATION_ERROR_IP_INVALID;
}

int ip_validate_private(const char *ip_str) {
    return validate_private_ip(ip_str) ? VALIDATION_SUCCESS : VALIDATION_ERROR_IP_INVALID;
}

int ip_validate_not_reserved(const char *ip_str) {
    return validate_reserved_ip(ip_str) ? VALIDATION_ERROR_IP_INVALID : VALIDATION_SUCCESS;
}

// 数值范围验证
int validate_uint8_range(uint8_t value, uint8_t min_val, uint8_t max_val) {
    if (value < min_val || value > max_val) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_uint16_range(uint16_t value, uint16_t min_val, uint16_t max_val) {
    if (value < min_val || value > max_val) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_uint32_range(uint32_t value, uint32_t min_val, uint32_t max_val) {
    if (value < min_val || value > max_val) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

// 配置项验证 (基于deprecated模块的业务逻辑)
int validate_voice_channel_count(uint16_t vchan_sum) {
    // 基于deprecated中的MAX_VOCODER_CHAN = 16, MAX_NOVOCODER_CHAN = 64
    if (vchan_sum == 0 || vchan_sum > 64) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_buffer_time(uint16_t buffer_time) {
    // 缓冲时间一般在1-1000ms范围内
    if (buffer_time == 0 || buffer_time > 1000) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_voice_down_time(uint16_t down_time) {
    // 语音结束时间一般在100-10000ms范围内
    if (down_time < 100 || down_time > 10000) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

int validate_peer_base_num(uint8_t peer_num) {
    // 基于deprecated中的PEER_MAX = 8
    if (peer_num == 0 || peer_num > 8) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

// MAC地址验证
int validate_mac_bytes(const uint8_t mac[6]) {
    if (!mac) {
        return VALIDATION_ERROR_INVALID_ARG;
    }
    
    // 检查是否为全零MAC地址
    int all_zero = 1;
    for (int i = 0; i < 6; i++) {
        if (mac[i] != 0) {
            all_zero = 0;
            break;
        }
    }
    
    if (all_zero) {
        return VALIDATION_ERROR_FORMAT_INVALID;
    }
    
    // 检查是否为广播MAC地址
    int all_ff = 1;
    for (int i = 0; i < 6; i++) {
        if (mac[i] != 0xFF) {
            all_ff = 0;
            break;
        }
    }
    
    if (all_ff) {
        return VALIDATION_ERROR_FORMAT_INVALID;
    }
    
    return VALIDATION_SUCCESS;
}

int validate_mac_string(const char *mac_str) {
    return validate_mac_address(mac_str) ? VALIDATION_SUCCESS : VALIDATION_ERROR_FORMAT_INVALID;
}

// 系统代码验证 (4位16进制)
int validate_system_code(uint16_t syscode) {
    // 系统代码不能为0
    if (syscode == 0) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

// 频点验证 (4位16进制)
int validate_frequency(uint16_t freq) {
    // 频点不能为0
    if (freq == 0) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

// 网络模式验证
int validate_network_mode(uint8_t mode) {
    // 基于deprecated中的枚举定义，通常0-2为有效值
    if (mode > 2) {
        return VALIDATION_ERROR_RANGE_INVALID;
    }
    return VALIDATION_SUCCESS;
}

// 设备类型验证
int validate_device_type(uint8_t type) {
    // 基于deprecated中enumBaseType的定义：0x10-0x18, 0x1F
    if ((type >= 0x10 && type <= 0x18) || type == 0x1F) {
        return VALIDATION_SUCCESS;
    }
    return VALIDATION_ERROR_RANGE_INVALID;
} 