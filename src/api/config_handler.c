#include "../../include/api/config_handler.h"
#include "../../include/config/network_config.h"
#include "../../include/config/device_config.h"
#include "../../include/core/response.h"

#include <stdio.h>
#include <string.h>

/**
 * 处理网络配置获取请求
 * GET /api/v1/config/network
 */
int handle_network_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    network_config_binary_t binary_config;
    cJSON *json_data = NULL;
    
    // 读取网络配置文件
    if (read_network_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read network configuration");
    }
    
    // 转换为JSON格式
    if (convert_network_binary_to_json(&binary_config, &json_data) != 0) {
        return response_send_error(connection, 500, "Failed to convert network configuration");
    }
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理网络配置保存请求
 * POST /api/v1/config/network
 */
int handle_network_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    network_config_binary_t binary_config;
    
    // JSON转换为二进制配置
    if (convert_network_json_to_binary(json, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid network configuration data");
    }
    
    // 写入配置文件
    if (write_network_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save network configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Network configuration saved successfully");
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 网络配置路由处理器
 */
int handle_config_network(struct MHD_Connection *connection, const char *method, 
                         const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return handle_network_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return handle_network_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 处理呼叫中心配置获取请求
 * GET /api/v1/config/device/center
 */
int handle_center_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    center_config_binary_t binary_config;
    center_config_json_t json_config;
    
    // 读取呼叫中心配置文件
    if (read_center_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read center configuration");
    }
    
    // 转换为JSON格式
    if (convert_center_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert center configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "center_no", json_config.center_no);
    cJSON_AddNumberToObject(json_data, "center_outssi", json_config.center_outssi);
    cJSON_AddNumberToObject(json_data, "center_inssi", json_config.center_inssi);
    cJSON_AddNumberToObject(json_data, "vchan_sum", json_config.vchan_sum);
    cJSON_AddNumberToObject(json_data, "center_voice_port", json_config.center_voice_port);
    cJSON_AddNumberToObject(json_data, "listen_agent_port", json_config.listen_agent_port);
    cJSON_AddNumberToObject(json_data, "peer_net_type", json_config.peer_net_type);
    cJSON_AddStringToObject(json_data, "send_all_agent_ip", json_config.send_all_agent_ip);
    cJSON_AddNumberToObject(json_data, "send_to_agent_port", json_config.send_to_agent_port);
    cJSON_AddNumberToObject(json_data, "inssi_num", json_config.inssi_num);
    cJSON_AddNumberToObject(json_data, "spec_function", json_config.spec_function);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理呼叫中心配置保存请求
 * POST /api/v1/config/device/center
 */
int handle_center_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    center_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "center_no");
    if (cJSON_IsNumber(item)) {
        json_config.center_no = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_outssi");
    if (cJSON_IsNumber(item)) {
        json_config.center_outssi = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_inssi");
    if (cJSON_IsNumber(item)) {
        json_config.center_inssi = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "vchan_sum");
    if (cJSON_IsNumber(item)) {
        json_config.vchan_sum = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_voice_port");
    if (cJSON_IsNumber(item)) {
        json_config.center_voice_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "listen_agent_port");
    if (cJSON_IsNumber(item)) {
        json_config.listen_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "peer_net_type");
    if (cJSON_IsNumber(item)) {
        json_config.peer_net_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "send_all_agent_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.send_all_agent_ip, item->valuestring, sizeof(json_config.send_all_agent_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "send_to_agent_port");
    if (cJSON_IsNumber(item)) {
        json_config.send_to_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "inssi_num");
    if (cJSON_IsNumber(item)) {
        json_config.inssi_num = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "spec_function");
    if (cJSON_IsNumber(item)) {
        json_config.spec_function = (uint16_t)item->valueint;
    }
    
    // 验证配置数据
    if (validate_center_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid center configuration data");
    }
    
    // JSON转换为二进制配置
    center_config_binary_t binary_config;
    if (convert_center_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    if (write_center_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save center configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Center configuration saved successfully");
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 呼叫中心配置路由处理器
 */
int handle_config_center(struct MHD_Connection *connection, const char *method, 
                        const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return handle_center_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return handle_center_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 处理基站配置获取请求
 * GET /api/v1/config/device/station/{type} 或 GET /api/v1/config/device/station
 */
int handle_station_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    station_config_binary_t binary_config;
    station_config_json_t json_config;
    
    // 读取基站配置文件
    if (read_station_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read station configuration");
    }
    
    // 转换为JSON格式
    if (convert_station_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert station configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "station_id", json_config.station_id);
    cJSON_AddNumberToObject(json_data, "station_type", json_config.station_type);
    cJSON_AddStringToObject(json_data, "station_type_name", 
                           get_station_type_string((station_type_t)json_config.station_type));
    
    // IP地址转换为字符串
    struct in_addr addr;
    addr.s_addr = json_config.center_ip;
    cJSON_AddStringToObject(json_data, "center_ip", inet_ntoa(addr));
    cJSON_AddNumberToObject(json_data, "center_port", json_config.center_port);
    
    addr.s_addr = json_config.local_ip;
    cJSON_AddStringToObject(json_data, "local_ip", inet_ntoa(addr));
    cJSON_AddNumberToObject(json_data, "local_port", json_config.local_port);
    
    cJSON_AddStringToObject(json_data, "station_name", json_config.station_name);
    cJSON_AddNumberToObject(json_data, "signal_strength", json_config.signal_strength);
    cJSON_AddNumberToObject(json_data, "voice_codec", json_config.voice_codec);
    cJSON_AddNumberToObject(json_data, "heartbeat_interval", json_config.heartbeat_interval);
    cJSON_AddBoolToObject(json_data, "auto_register", json_config.auto_register);
    cJSON_AddNumberToObject(json_data, "group_id", json_config.group_id);
    cJSON_AddNumberToObject(json_data, "frequency_band", json_config.frequency_band);
    cJSON_AddNumberToObject(json_data, "power_level", json_config.power_level);
    cJSON_AddStringToObject(json_data, "module_version", json_config.module_version);
    cJSON_AddNumberToObject(json_data, "network_mode", json_config.network_mode);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理基站配置保存请求
 * POST /api/v1/config/device/station/{type} 或 POST /api/v1/config/device/station
 */
int handle_station_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    station_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "station_id");
    if (cJSON_IsNumber(item)) {
        json_config.station_id = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "station_type");
    if (cJSON_IsNumber(item)) {
        json_config.station_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_ip");
    if (cJSON_IsString(item)) {
        json_config.center_ip = inet_addr(item->valuestring);
    }
    
    item = cJSON_GetObjectItem(json, "center_port");
    if (cJSON_IsNumber(item)) {
        json_config.center_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_ip");
    if (cJSON_IsString(item)) {
        json_config.local_ip = inet_addr(item->valuestring);
    }
    
    item = cJSON_GetObjectItem(json, "local_port");
    if (cJSON_IsNumber(item)) {
        json_config.local_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "station_name");
    if (cJSON_IsString(item)) {
        strncpy(json_config.station_name, item->valuestring, sizeof(json_config.station_name) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "signal_strength");
    if (cJSON_IsNumber(item)) {
        json_config.signal_strength = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "voice_codec");
    if (cJSON_IsNumber(item)) {
        json_config.voice_codec = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "heartbeat_interval");
    if (cJSON_IsNumber(item)) {
        json_config.heartbeat_interval = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "auto_register");
    if (cJSON_IsBool(item)) {
        json_config.auto_register = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "group_id");
    if (cJSON_IsNumber(item)) {
        json_config.group_id = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "frequency_band");
    if (cJSON_IsNumber(item)) {
        json_config.frequency_band = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "power_level");
    if (cJSON_IsNumber(item)) {
        json_config.power_level = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "module_version");
    if (cJSON_IsString(item)) {
        strncpy(json_config.module_version, item->valuestring, sizeof(json_config.module_version) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "network_mode");
    if (cJSON_IsNumber(item)) {
        json_config.network_mode = (uint8_t)item->valueint;
    }
    
    // 验证配置数据
    if (validate_station_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid station configuration data");
    }
    
    // JSON转换为二进制配置
    station_config_binary_t binary_config;
    if (convert_station_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    if (write_station_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save station configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Station configuration saved successfully");
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

// 移除了设备类型检测API，因为旧项目中不存在此功能
// 保持与旧项目100%功能一致

/**
 * 基站配置路由处理器
 */
int handle_config_station(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return station_handle_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return station_handle_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 处理交换机配置获取请求
 * GET /api/v1/config/device/switch
 */
int handle_switch_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    switch_config_binary_t binary_config;
    switch_config_json_t json_config;
    
    // 读取交换机配置文件
    if (read_switch_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read switch configuration");
    }
    
    // 转换为JSON格式
    if (convert_switch_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert switch configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "switch_id", json_config.switch_id);
    cJSON_AddStringToObject(json_data, "switch_name", json_config.switch_name);
    cJSON_AddNumberToObject(json_data, "switch_mode", json_config.switch_mode);
    cJSON_AddNumberToObject(json_data, "board_type", json_config.board_type);
    cJSON_AddNumberToObject(json_data, "center_ip", json_config.center_ip);
    cJSON_AddNumberToObject(json_data, "center_port", json_config.center_port);
    cJSON_AddNumberToObject(json_data, "local_ip", json_config.local_ip);
    cJSON_AddNumberToObject(json_data, "local_port", json_config.local_port);
    cJSON_AddNumberToObject(json_data, "channel_count", json_config.channel_count);
    cJSON_AddNumberToObject(json_data, "voice_start_port", json_config.voice_start_port);
    cJSON_AddNumberToObject(json_data, "data_start_port", json_config.data_start_port);
    cJSON_AddNumberToObject(json_data, "timeout_interval", json_config.timeout_interval);
    cJSON_AddNumberToObject(json_data, "priority_level", json_config.priority_level);
    cJSON_AddNumberToObject(json_data, "qos_enabled", json_config.qos_enabled);
    cJSON_AddStringToObject(json_data, "firmware_version", json_config.firmware_version);
    cJSON_AddNumberToObject(json_data, "auto_failover", json_config.auto_failover);
    cJSON_AddNumberToObject(json_data, "backup_server_ip", json_config.backup_server_ip);
    cJSON_AddNumberToObject(json_data, "backup_server_port", json_config.backup_server_port);
    
    // 检测板卡类型信息
    board_info_t board_info;
    if (detect_board_type(&board_info) == 0) {
        cJSON_AddNumberToObject(json_data, "detected_board_type", board_info.board_type);
        cJSON_AddStringToObject(json_data, "board_type_name", get_board_type_string(board_info.board_type));
        cJSON_AddStringToObject(json_data, "board_name", board_info.board_name);
        cJSON_AddStringToObject(json_data, "board_version", board_info.board_version);
    }
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理交换机配置保存请求
 * POST /api/v1/config/device/switch
 */
int handle_switch_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    switch_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "switch_id");
    if (cJSON_IsNumber(item)) {
        json_config.switch_id = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "switch_name");
    if (cJSON_IsString(item)) {
        strncpy(json_config.switch_name, item->valuestring, sizeof(json_config.switch_name) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "switch_mode");
    if (cJSON_IsNumber(item)) {
        json_config.switch_mode = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "board_type");
    if (cJSON_IsNumber(item)) {
        json_config.board_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_ip");
    if (cJSON_IsNumber(item)) {
        json_config.center_ip = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_port");
    if (cJSON_IsNumber(item)) {
        json_config.center_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_ip");
    if (cJSON_IsNumber(item)) {
        json_config.local_ip = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_port");
    if (cJSON_IsNumber(item)) {
        json_config.local_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "channel_count");
    if (cJSON_IsNumber(item)) {
        json_config.channel_count = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "voice_start_port");
    if (cJSON_IsNumber(item)) {
        json_config.voice_start_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "data_start_port");
    if (cJSON_IsNumber(item)) {
        json_config.data_start_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "timeout_interval");
    if (cJSON_IsNumber(item)) {
        json_config.timeout_interval = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "priority_level");
    if (cJSON_IsNumber(item)) {
        json_config.priority_level = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "qos_enabled");
    if (cJSON_IsBool(item)) {
        json_config.qos_enabled = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "firmware_version");
    if (cJSON_IsString(item)) {
        strncpy(json_config.firmware_version, item->valuestring, sizeof(json_config.firmware_version) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "auto_failover");
    if (cJSON_IsBool(item)) {
        json_config.auto_failover = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "backup_server_ip");
    if (cJSON_IsNumber(item)) {
        json_config.backup_server_ip = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "backup_server_port");
    if (cJSON_IsNumber(item)) {
        json_config.backup_server_port = (uint16_t)item->valueint;
    }
    
    // 验证配置数据
    if (validate_switch_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid switch configuration data");
    }
    
    // JSON转换为二进制配置
    switch_config_binary_t binary_config;
    if (convert_switch_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    if (write_switch_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save switch configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Switch configuration saved successfully");
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 交换机配置路由处理器
 */
int handle_config_switch(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return switch_handle_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return switch_handle_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 处理录音基站配置获取请求
 * GET /api/v1/config/device/recorder
 */
int handle_recorder_base_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    // 检测设备类型
    recorder_base_device_type_t device_type = detect_recorder_base_device_type();
    
    recorder_base_config_t binary_config;
    recorder_base_config_json_t json_config;
    
    // 读取录音基站配置
    if (read_recorder_base_config(&binary_config, device_type) != 0) {
        return response_send_error(connection, 500, "Failed to read recorder base configuration");
    }
    
    // 转换为JSON格式
    if (convert_recorder_base_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert recorder base configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    
    // 网络配置
    cJSON_AddStringToObject(json_data, "ip", json_config.ip);
    cJSON_AddStringToObject(json_data, "mask", json_config.mask);
    cJSON_AddStringToObject(json_data, "gateway", json_config.gateway);
    cJSON_AddStringToObject(json_data, "dns", json_config.dns);
    cJSON_AddStringToObject(json_data, "mac", json_config.mac);
    
    // 通用配置
    cJSON_AddNumberToObject(json_data, "ds", json_config.ds);
    cJSON_AddNumberToObject(json_data, "sw", json_config.sw);
    cJSON_AddNumberToObject(json_data, "conf_num", json_config.conf_num);
    cJSON_AddNumberToObject(json_data, "normal_num", json_config.normal_num);
    
    // 板卡基础配置
    cJSON_AddStringToObject(json_data, "daemon_ip", json_config.daemon_ip);
    cJSON_AddNumberToObject(json_data, "daemon_port", json_config.daemon_port);
    cJSON_AddStringToObject(json_data, "log_ip", json_config.log_ip);
    cJSON_AddNumberToObject(json_data, "log_port", json_config.log_port);
    cJSON_AddStringToObject(json_data, "cfg_ip", json_config.cfg_ip);
    cJSON_AddNumberToObject(json_data, "cfg_port", json_config.cfg_port);
    cJSON_AddNumberToObject(json_data, "log_level", json_config.log_level);
    cJSON_AddNumberToObject(json_data, "log_to_where", json_config.log_to_where);
    cJSON_AddNumberToObject(json_data, "data_listen_port", json_config.data_listen_port);
    cJSON_AddNumberToObject(json_data, "data_send_port", json_config.data_send_port);
    
    // 会议配置
    cJSON_AddNumberToObject(json_data, "work_mode", json_config.work_mode);
    cJSON_AddNumberToObject(json_data, "spec_function", json_config.spec_function);
    cJSON_AddNumberToObject(json_data, "peer_base_num", json_config.peer_base_num);
    cJSON_AddStringToObject(json_data, "voice_ip", json_config.voice_ip);
    cJSON_AddNumberToObject(json_data, "vbus_base_port", json_config.vbus_base_port);
    cJSON_AddNumberToObject(json_data, "vchan_number", json_config.vchan_number);
    cJSON_AddNumberToObject(json_data, "buffertime", json_config.buffertime);
    cJSON_AddNumberToObject(json_data, "downtime", json_config.downtime);
    
    // 对端配置数组
    cJSON *peer_array = cJSON_CreateArray();
    for (int i = 0; i < 8; i++) {
        cJSON *peer_obj = cJSON_CreateObject();
        cJSON_AddStringToObject(peer_obj, "peer_ip", json_config.peer_configs[i].peer_ip);
        cJSON_AddStringToObject(peer_obj, "peer_voice_ip", json_config.peer_configs[i].peer_voice_ip);
        cJSON_AddNumberToObject(peer_obj, "peer_data_listen_port", json_config.peer_configs[i].peer_data_listen_port);
        cJSON_AddNumberToObject(peer_obj, "peer_voice_port_base", json_config.peer_configs[i].peer_voice_port_base);
        cJSON_AddNumberToObject(peer_obj, "peer_net_address", json_config.peer_configs[i].peer_net_address);
        cJSON_AddNumberToObject(peer_obj, "peer_type", json_config.peer_configs[i].peer_type);
        
        cJSON *vbus_array = cJSON_CreateArray();
        for (int j = 0; j < 12; j++) {
            cJSON_AddItemToArray(vbus_array, cJSON_CreateNumber(json_config.peer_configs[i].peer_vbus_to_chan[j]));
        }
        cJSON_AddItemToObject(peer_obj, "peer_vbus_to_chan", vbus_array);
        
        cJSON_AddItemToArray(peer_array, peer_obj);
    }
    cJSON_AddItemToObject(json_data, "peer_configs", peer_array);
    
    // 设备信息
    cJSON_AddNumberToObject(json_data, "device_type", json_config.device_type);
    cJSON_AddStringToObject(json_data, "device_name", json_config.device_name);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理录音基站配置保存请求
 * POST /api/v1/config/device/recorder
 */
int handle_recorder_base_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    recorder_base_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    // 网络配置
    item = cJSON_GetObjectItem(json, "ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.ip, item->valuestring, sizeof(json_config.ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "mask");
    if (cJSON_IsString(item)) {
        strncpy(json_config.mask, item->valuestring, sizeof(json_config.mask) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "gateway");
    if (cJSON_IsString(item)) {
        strncpy(json_config.gateway, item->valuestring, sizeof(json_config.gateway) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "dns");
    if (cJSON_IsString(item)) {
        strncpy(json_config.dns, item->valuestring, sizeof(json_config.dns) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "mac");
    if (cJSON_IsString(item)) {
        strncpy(json_config.mac, item->valuestring, sizeof(json_config.mac) - 1);
    }
    
    // 通用配置
    item = cJSON_GetObjectItem(json, "ds");
    if (cJSON_IsNumber(item)) {
        json_config.ds = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "sw");
    if (cJSON_IsNumber(item)) {
        json_config.sw = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "conf_num");
    if (cJSON_IsNumber(item)) {
        json_config.conf_num = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "normal_num");
    if (cJSON_IsNumber(item)) {
        json_config.normal_num = (uint8_t)item->valueint;
    }
    
    // 板卡基础配置
    item = cJSON_GetObjectItem(json, "daemon_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.daemon_ip, item->valuestring, sizeof(json_config.daemon_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "daemon_port");
    if (cJSON_IsNumber(item)) {
        json_config.daemon_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "log_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.log_ip, item->valuestring, sizeof(json_config.log_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "log_port");
    if (cJSON_IsNumber(item)) {
        json_config.log_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "cfg_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.cfg_ip, item->valuestring, sizeof(json_config.cfg_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "cfg_port");
    if (cJSON_IsNumber(item)) {
        json_config.cfg_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "log_level");
    if (cJSON_IsNumber(item)) {
        json_config.log_level = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "log_to_where");
    if (cJSON_IsNumber(item)) {
        json_config.log_to_where = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "data_listen_port");
    if (cJSON_IsNumber(item)) {
        json_config.data_listen_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "data_send_port");
    if (cJSON_IsNumber(item)) {
        json_config.data_send_port = (uint16_t)item->valueint;
    }
    
    // 会议配置
    item = cJSON_GetObjectItem(json, "work_mode");
    if (cJSON_IsNumber(item)) {
        json_config.work_mode = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "spec_function");
    if (cJSON_IsNumber(item)) {
        json_config.spec_function = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "peer_base_num");
    if (cJSON_IsNumber(item)) {
        json_config.peer_base_num = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "voice_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.voice_ip, item->valuestring, sizeof(json_config.voice_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "vbus_base_port");
    if (cJSON_IsNumber(item)) {
        json_config.vbus_base_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "vchan_number");
    if (cJSON_IsNumber(item)) {
        json_config.vchan_number = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "buffertime");
    if (cJSON_IsNumber(item)) {
        json_config.buffertime = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "downtime");
    if (cJSON_IsNumber(item)) {
        json_config.downtime = (uint16_t)item->valueint;
    }
    
    // 设备类型
    item = cJSON_GetObjectItem(json, "device_type");
    if (cJSON_IsNumber(item)) {
        json_config.device_type = (uint8_t)item->valueint;
    }
    
    // 验证配置数据
    if (validate_recorder_base_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid recorder base configuration data");
    }
    
    recorder_base_config_t binary_config;
    
    // JSON转换为二进制配置
    if (convert_recorder_base_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    recorder_base_device_type_t device_type = (recorder_base_device_type_t)json_config.device_type;
    if (write_recorder_base_config(&binary_config, device_type) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save recorder base configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Recorder base configuration saved successfully");
    cJSON_AddStringToObject(success_data, "device_name", get_recorder_base_device_name(device_type));
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 录音基站配置路由处理器
 */
int handle_config_recorder(struct MHD_Connection *connection, const char *method,
                          const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return recorder_handle_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return recorder_handle_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 网关配置路由处理器
 */
int handle_config_gateway(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return handle_gateway_config_get_api(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return handle_gateway_config_post_api(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
}

/**
 * 处理网关配置GET请求的API包装器
 */
int handle_gateway_config_get_api(struct MHD_Connection *connection, const char *url, const char *request_data) {
    (void)url;
    (void)request_data;

    char *response_data = NULL;
    int ret = handle_gateway_config_get(&response_data);

    if (ret < 0 || !response_data) {
        if (response_data) free(response_data);
        return response_send_error(connection, 500, "Failed to read gateway configuration");
    }

    // 解析JSON响应以便使用标准响应函数
    cJSON *json_response = cJSON_Parse(response_data);
    free(response_data);

    if (!json_response) {
        return response_send_error(connection, 500, "Failed to parse gateway configuration");
    }

    int result = response_send_success(connection, json_response);
    cJSON_Delete(json_response);

    return result;
}

/**
 * 处理网关配置POST请求的API包装器
 */
int handle_gateway_config_post_api(struct MHD_Connection *connection, const char *url, const char *request_data) {
    (void)url;

    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }

    char *response_data = NULL;
    int ret = handle_gateway_config_post(request_data, &response_data);

    if (ret < 0) {
        if (response_data) {
            // 错误响应
            cJSON *error_json = cJSON_Parse(response_data);
            free(response_data);
            if (error_json) {
                int result = response_send_error_json(connection, 400, error_json);
                cJSON_Delete(error_json);
                return result;
            }
        }
        return response_send_error(connection, 400, "Failed to save gateway configuration");
    }

    // 成功响应
    cJSON *success_json = cJSON_Parse(response_data);
    free(response_data);

    if (!success_json) {
        return response_send_error(connection, 500, "Failed to parse response");
    }

    int result = response_send_success(connection, success_json);
    cJSON_Delete(success_json);

    return result;
}

/**
 * 配置请求处理器 - 主路由分发
 */
int handle_config_request(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data) {
    // 网络配置路由
    if (strncmp(url, "/api/v1/config/network", 22) == 0) {
        return handle_config_network(connection, method, url, request_data);
    }
    // 呼叫中心配置路由
    else if (strncmp(url, "/api/v1/config/device/center", 29) == 0) {
        return handle_config_center(connection, method, url, request_data);
    }
    // 基站配置路由
    else if (strncmp(url, "/api/v1/config/device/station", 30) == 0) {
        return handle_config_station(connection, method, url, request_data);
    }
    // 交换机配置路由
    else if (strncmp(url, "/api/v1/config/device/switch", 29) == 0) {
        return handle_config_switch(connection, method, url, request_data);
    }
    // 录音基站配置路由
    else if (strncmp(url, "/api/v1/config/device/recorder", 31) == 0) {
        return handle_config_recorder(connection, method, url, request_data);
    }
    // 网关配置路由
    else if (strncmp(url, "/api/v1/config/device/gateway", 30) == 0) {
        return handle_config_gateway(connection, method, url, request_data);
    }
    // 设备类型检测路由
    else if (strncmp(url, "/api/v1/system/device/type", 27) == 0) {
        return handle_device_type_get(connection, url, request_data);
    }
    else {
        return response_send_error(connection, 404, "Configuration endpoint not found");
    }
}

int config_handle_network_get(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data) {
    // TODO: 完整实现网络配置获取
    (void)connection;
    (void)url;
    (void)request_data;
    return MHD_NO;
}

int config_handle_network_post(struct MHD_Connection *connection,
                              const char *url,
                              cJSON *request_data) {
    // TODO: 完整实现网络配置保存
    (void)connection;
    (void)url;
    (void)request_data;
    return MHD_NO;
}

int config_handle_device_get(struct MHD_Connection *connection,
                            const char *url,
                            cJSON *request_data) {
    // TODO: 完整实现设备配置获取
    (void)connection;
    (void)url;
    (void)request_data;
    return MHD_NO;
}

int config_handle_device_post(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data) {
    // TODO: 完整实现设备配置保存
    (void)connection;
    (void)url;
    (void)request_data;
    return MHD_NO;
} 