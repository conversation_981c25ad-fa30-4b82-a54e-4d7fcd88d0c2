/**
 * @file system_handler.c
 * @brief 系统管理API处理器 - 严格基于旧项目功能一致性
 * <AUTHOR> Team
 * @date 2024
 * 
 * 严格基于旧项目0system.c、0passwd.c、0ntp.c、0down.c
 * 不增加任何旧项目中不存在的功能
 */

#include "api/system_handler.h"
#include "config/system_config.h"
#include "core/response.h"
#include "core/http_server.h"
#include <stdlib.h>
#include <string.h>

/**
 * 系统重启处理 - 基于旧项目0system.c
 */
int system_handle_reboot(struct MHD_Connection *connection,
                        const char *url,
                        cJSON *request_data) {
    (void)url;
    (void)request_data;

    // 执行系统重启
    config_result_t result = system_reboot();
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "系统重启失败");
    }

    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "系统重启指令已发送");
    int ret = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return ret;
}

/**
 * 密码修改处理 - 基于旧项目0passwd.c
 */
int system_handle_password_change(struct MHD_Connection *connection,
                                 const char *url,
                                 cJSON *request_data) {
    (void)url;

    if (!request_data) {
        return response_send_error(connection, HTTP_BAD_REQUEST,
                                  "请求数据无效");
    }

    // 解析请求数据
    password_change_request_t request;
    memset(&request, 0, sizeof(request));

    cJSON *old_pwd = cJSON_GetObjectItemCaseSensitive(request_data, "old_password");
    cJSON *new_pwd1 = cJSON_GetObjectItemCaseSensitive(request_data, "new_password1");
    cJSON *new_pwd2 = cJSON_GetObjectItemCaseSensitive(request_data, "new_password2");

    if (!cJSON_IsString(old_pwd) || !cJSON_IsString(new_pwd1) || !cJSON_IsString(new_pwd2)) {
        return response_send_error(connection, HTTP_BAD_REQUEST,
                                  "密码字段无效");
    }

    strncpy(request.old_password, old_pwd->valuestring, sizeof(request.old_password) - 1);
    strncpy(request.new_password1, new_pwd1->valuestring, sizeof(request.new_password1) - 1);
    strncpy(request.new_password2, new_pwd2->valuestring, sizeof(request.new_password2) - 1);

    // 执行密码修改
    user_operation_result_t result = user_change_password("admin", &request);
    
    const char *error_message = NULL;
    switch (result) {
        case USER_OP_SUCCESS:
            {
                cJSON *success_data = cJSON_CreateObject();
                cJSON_AddStringToObject(success_data, "message", "密码修改成功");
                int ret = response_send_success(connection, success_data);
                cJSON_Delete(success_data);
                return ret;
            }
        case USER_OP_PASSWORD_MISMATCH:
            error_message = "两次输入的新密码不一致";
            break;
        case USER_OP_PASSWORD_TOO_SHORT:
            error_message = "密码长度太短，至少4位";
            break;
        case USER_OP_PASSWORD_TOO_LONG:
            error_message = "密码长度太长，最多20位";
            break;
        default:
            error_message = "密码修改失败";
            break;
    }

    return response_send_error(connection, HTTP_BAD_REQUEST, error_message);
}

/**
 * NTP配置获取处理 - 基于旧项目0ntp.c
 */
int system_handle_ntp_get(struct MHD_Connection *connection,
                         const char *url,
                         cJSON *request_data) {
    (void)url;
    (void)request_data;

    ntp_config_t config;

    // 读取NTP配置
    config_result_t result = ntp_read_config(&config);
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "读取NTP配置失败");
    }

    // 创建响应数据
    cJSON *response_data = cJSON_CreateObject();
    if (!response_data) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "内存分配失败");
    }

    cJSON_AddBoolToObject(response_data, "ntp_enabled", config.ntp_enabled);
    cJSON_AddBoolToObject(response_data, "ntp_client_mode", config.ntp_client_mode);
    cJSON_AddBoolToObject(response_data, "ntp_server_mode", config.ntp_server_mode);
    cJSON_AddStringToObject(response_data, "ntp_server", config.ntp_server);
    cJSON_AddNumberToObject(response_data, "ntp_port", config.ntp_port);
    cJSON_AddNumberToObject(response_data, "sync_interval", config.sync_interval);
    cJSON_AddNumberToObject(response_data, "timezone_offset", config.timezone_offset);

    int ret = response_send_success(connection, response_data);
    cJSON_Delete(response_data);
    return ret;
}

/**
 * NTP配置保存处理 - 基于旧项目0ntp.c
 */
int system_handle_ntp_post(struct MHD_Connection *connection,
                          const char *url,
                          cJSON *request_data) {
    (void)url;

    if (!request_data) {
        return response_send_error(connection, HTTP_BAD_REQUEST,
                                  "请求数据无效");
    }

    ntp_config_t config;
    memset(&config, 0, sizeof(config));

    // 从JSON解析配置
    cJSON *item;

    item = cJSON_GetObjectItemCaseSensitive(request_data, "ntp_enabled");
    if (cJSON_IsBool(item)) {
        config.ntp_enabled = cJSON_IsTrue(item) ? 1 : 0;
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "ntp_client_mode");
    if (cJSON_IsBool(item)) {
        config.ntp_client_mode = cJSON_IsTrue(item) ? 1 : 0;
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "ntp_server_mode");
    if (cJSON_IsBool(item)) {
        config.ntp_server_mode = cJSON_IsTrue(item) ? 1 : 0;
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "ntp_server");
    if (cJSON_IsString(item) && item->valuestring) {
        strncpy(config.ntp_server, item->valuestring, sizeof(config.ntp_server) - 1);
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "ntp_port");
    if (cJSON_IsNumber(item)) {
        config.ntp_port = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "sync_interval");
    if (cJSON_IsNumber(item)) {
        config.sync_interval = (uint32_t)item->valueint;
    }

    item = cJSON_GetObjectItemCaseSensitive(request_data, "timezone_offset");
    if (cJSON_IsNumber(item)) {
        config.timezone_offset = (uint8_t)item->valueint;
    }

    // 验证配置
    config_result_t result = ntp_validate_config(&config);
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_BAD_REQUEST,
                                  "NTP配置验证失败");
    }

    // 保存配置
    result = ntp_write_config(&config);
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "NTP配置保存失败");
    }

    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "NTP配置保存成功");
    int ret = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return ret;
}

/**
 * 日志获取处理 - 基于旧项目0down.c
 */
int system_handle_logs(struct MHD_Connection *connection,
                      const char *url,
                      cJSON *request_data) {
    (void)url;

    // 获取查询参数
    log_type_t log_type = LOG_TYPE_STARTUP;  // 默认启动日志
    
    if (request_data) {
        cJSON *type_item = cJSON_GetObjectItemCaseSensitive(request_data, "type");
        if (cJSON_IsNumber(type_item)) {
            log_type = (log_type_t)type_item->valueint;
        }
    }

    // 读取日志文件
    char buffer[8192];
    config_result_t result = log_read_file(log_type, buffer, sizeof(buffer));
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "读取日志文件失败");
    }

    // 创建响应数据
    cJSON *response_data = cJSON_CreateObject();
    if (!response_data) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "内存分配失败");
    }

    cJSON_AddNumberToObject(response_data, "log_type", log_type);
    cJSON_AddStringToObject(response_data, "log_content", buffer);

    int ret = response_send_success(connection, response_data);
    cJSON_Delete(response_data);
    return ret;
}

/**
 * 信号质量检测处理 - 基于旧项目0down.c
 */
int system_handle_signal_quality(struct MHD_Connection *connection,
                                const char *url,
                                cJSON *request_data) {
    (void)url;
    (void)request_data;

    signal_quality_t quality;

    // 获取信号质量
    config_result_t result = log_get_signal_quality(&quality);
    if (result != CONFIG_RESULT_SUCCESS) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "获取信号质量失败");
    }

    // 创建响应数据
    cJSON *response_data = cJSON_CreateObject();
    if (!response_data) {
        return response_send_error(connection, HTTP_INTERNAL_ERROR,
                                  "内存分配失败");
    }

    cJSON_AddStringToObject(response_data, "rssi", quality.rssi);
    cJSON_AddStringToObject(response_data, "ber", quality.ber);
    cJSON_AddStringToObject(response_data, "dbm", quality.dbm);
    cJSON_AddStringToObject(response_data, "error_message", quality.error_message);
    cJSON_AddNumberToObject(response_data, "detection_time", (double)quality.detection_time);

    int ret = response_send_success(connection, response_data);
    cJSON_Delete(response_data);
    return ret;
} 