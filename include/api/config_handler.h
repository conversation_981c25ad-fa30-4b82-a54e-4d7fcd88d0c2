#ifndef WEBCFG_CONFIG_HANDLER_H
#define WEBCFG_CONFIG_HANDLER_H

#include <microhttpd.h>
#include <cJSON.h>

/**
 * 网络配置获取处理
 */
int config_handle_network_get(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data);

/**
 * 网络配置保存处理
 */
int config_handle_network_post(struct MHD_Connection *connection,
                              const char *url,
                              cJSON *request_data);

/**
 * 设备配置获取处理
 */
int config_handle_device_get(struct MHD_Connection *connection,
                            const char *url,
                            cJSON *request_data);

/**
 * 设备配置保存处理
 */
int config_handle_device_post(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data);

/**
 * 录音基站配置路由处理器
 */
int handle_config_recorder(struct MHD_Connection *connection, const char *method,
                          const char *url, const char *request_data);

/**
 * 网关配置路由处理器
 */
int handle_config_gateway(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data);

/**
 * 网关配置GET请求处理器
 */
int handle_gateway_config_get_api(struct MHD_Connection *connection,
                                 const char *url, const char *request_data);

/**
 * 网关配置POST请求处理器
 */
int handle_gateway_config_post_api(struct MHD_Connection *connection,
                                  const char *url, const char *request_data);

/**
 * 主配置请求处理器
 */
int handle_config_request(struct MHD_Connection *connection, const char *method,
                         const char *url, const char *request_data);

#endif // WEBCFG_CONFIG_HANDLER_H