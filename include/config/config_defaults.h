#ifndef CONFIG_DEFAULTS_H
#define CONFIG_DEFAULTS_H

/**
 * @file config_defaults.h
 * @brief 配置默认值统一定义
 * <AUTHOR> Team
 * @date 2024-12-26
 * 
 * 统一管理所有配置模块的默认值，避免硬编码重复
 * 解决代码冗余问题，提供一致的默认配置
 */

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 网络相关默认值 ====================

// IP地址默认值
#define DEFAULT_LOCAL_IP                "*************"
#define DEFAULT_VOICE_IP                "*************"
#define DEFAULT_DAEMON_IP               "*************"
#define DEFAULT_LOG_IP                  "*************"
#define DEFAULT_CFG_IP                  "*************"
#define DEFAULT_SEND_ALL_AGENT_IP       "*************"
#define DEFAULT_GATEWAY_IP              "***********"
#define DEFAULT_NETMASK                 "*************"
#define DEFAULT_DNS_PRIMARY             "*******"
#define DEFAULT_DNS_SECONDARY           "*******"

// 端口默认值
#define DEFAULT_VOICE_PORT              3000
#define DEFAULT_VOICE_PORT_BASE         3000
#define DEFAULT_DATA_LISTEN_PORT        2800
#define DEFAULT_DATA_SEND_PORT          2900
#define DEFAULT_DAEMON_PORT             2700
#define DEFAULT_LOG_PORT                2600
#define DEFAULT_CFG_PORT                2500
#define DEFAULT_LISTEN_AGENT_PORT       2800
#define DEFAULT_SEND_TO_AGENT_PORT      2900
#define DEFAULT_VBUS_BASE_PORT          4000

// MAC地址默认值
#define DEFAULT_MAC_ADDRESS             "00:11:22:33:44:55"

// ==================== 设备标识默认值 ====================

// 24位地址和ID默认值
#define DEFAULT_CENTER_NO               0x000001
#define DEFAULT_CENTER_OUTSSI           0x000001
#define DEFAULT_CENTER_INSSI            0x000001
#define DEFAULT_NET_ADDRESS             0x000001
#define DEFAULT_GATEWAY_NO              0x000001
#define DEFAULT_STATION_ID              0x000001
#define DEFAULT_RECORDER_ID             0x000001
#define DEFAULT_MINI_ID                 0x000001

// 设备名称默认值
#define DEFAULT_CENTER_NAME             "呼叫中心"
#define DEFAULT_GATEWAY_NAME            "网关设备"
#define DEFAULT_STATION_NAME            "基站设备"
#define DEFAULT_RECORDER_NAME           "录音基站"
#define DEFAULT_MINI_NAME               "最小基站"
#define DEFAULT_SWITCH_NAME             "交换机"

// ==================== 系统配置默认值 ====================

// 日志配置默认值
#define DEFAULT_LOG_LEVEL               1       // INFO级别
#define DEFAULT_LOG_TO_WHERE            0       // 记录到文件

// 网络模式默认值
#define DEFAULT_GET_CFG_METHOD          0       // 默认获取配置方法
#define DEFAULT_NETWORK_MODE            0       // 默认网络模式
#define DEFAULT_PEER_NET_TYPE           0       // 默认对等网络类型

// 通道和容量默认值
#define DEFAULT_VCHAN_SUM               8       // 默认语音通道数
#define DEFAULT_VCAN_NUMBER             4       // 默认VCAN数量
#define DEFAULT_INSSI_NUM               100     // 默认内应号个数
#define DEFAULT_PEER_BASE_NUM           4       // 默认对端基站数

// 时间相关默认值
#define DEFAULT_BUFFERTIME              100     // 默认缓冲时间(ms)
#define DEFAULT_DOWNTIME                5000    // 默认掉线时间(ms)
#define DEFAULT_RESETTIME               30      // 默认重置时间(s)
#define DEFAULT_HEARTBEAT_INTERVAL      30      // 默认心跳间隔(s)

// 功能开关默认值
#define DEFAULT_SPEC_FUNCTION           0       // 默认特殊功能关闭
#define DEFAULT_AUTO_REGISTER           1       // 默认自动注册开启
#define DEFAULT_AUTO_BACKUP             0       // 默认自动备份关闭
#define DEFAULT_NOISE_REDUCTION         1       // 默认噪声抑制开启
#define DEFAULT_EMERGENCY_MODE          0       // 默认应急模式关闭

// ==================== 录音模块默认值 ====================

// 音频格式默认值
#define DEFAULT_AUDIO_FORMAT            0       // PCM格式
#define DEFAULT_SAMPLE_RATE             8000    // 8kHz采样率
#define DEFAULT_CHANNELS                1       // 单声道
#define DEFAULT_BIT_DEPTH               16      // 16位深度
#define DEFAULT_COMPRESSION_LEVEL       5       // 中等压缩级别

// 录音存储默认值
#define DEFAULT_MAX_FILE_SIZE           100     // 100MB最大文件大小
#define DEFAULT_MAX_RECORD_TIME         60      // 60分钟最大录音时间
#define DEFAULT_STORAGE_PATH            "/var/record"

// ==================== 最小基站默认值 ====================

// 物理参数默认值
#define DEFAULT_MINI_TYPE               0       // 室内类型
#define DEFAULT_POWER_LEVEL             3       // 中等功率
#define DEFAULT_FREQUENCY               400     // 400MHz工作频率
#define DEFAULT_CHANNEL_COUNT           4       // 4个信道
#define DEFAULT_COVERAGE_RADIUS         1000    // 1000米覆盖半径
#define DEFAULT_ANTENNA_GAIN            3       // 3dBi天线增益

// 固件版本默认值
#define DEFAULT_FIRMWARE_VERSION        "1.0.0"

// ==================== 交换机默认值 ====================

// 交换模式默认值
#define DEFAULT_SWITCH_MODE             0       // 基础交换模式
#define DEFAULT_SWITCH_TYPE             0       // 基础交换机类型

// 3G/WLAN配置默认值
#define DEFAULT_3G_MODE                 0       // 不使用3G
#define DEFAULT_DYNIP_MODE              0       // 不需要动态IP
#define DEFAULT_WLAN_MODE               0       // 不使用WLAN
#define DEFAULT_DHCP_ENABLE             1       // 启用DHCP

// DDNS默认值
#define DEFAULT_DDNS_PROVIDER           0       // 3322提供商
#define DEFAULT_DDNS_USERNAME           "admin"
#define DEFAULT_DDNS_PASSWORD           "admin"
#define DEFAULT_DDNS_URL                "members.3322.org"

// VPN默认值
#define DEFAULT_VPN_APN                 "cmnet"
#define DEFAULT_VPN_USERNAME            "card"
#define DEFAULT_VPN_PASSWORD            "card"
#define DEFAULT_VPN_AUTH_TYPE           0       // None认证

// ==================== 文件路径默认值 ====================

// 配置文件路径
#define DEFAULT_NETWORK_CONFIG_PATH     "/home/<USER>/cfg/network.cfg"
#define DEFAULT_CENTER_CONFIG_PATH      "/home/<USER>/cfg/center.cfg"
#define DEFAULT_GATEWAY_CONFIG_PATH     "/home/<USER>/cfg/gateway.cfg"
#define DEFAULT_STATION_CONFIG_PATH     "/home/<USER>/cfg/station.cfg"
#define DEFAULT_RECORDER_CONFIG_PATH    "/home/<USER>/cfg/recorder.cfg"
#define DEFAULT_MINI_CONFIG_PATH        "/home/<USER>/cfg/mini.cfg"
#define DEFAULT_SWITCH_CONFIG_PATH      "/home/<USER>/cfg/switch.cfg"

// 系统配置文件路径
#define DEFAULT_COMMON_CONFIG_PATH      "/home/<USER>/cfg/common.cfg"
#define DEFAULT_BOARD_CONFIG_PATH       "/home/<USER>/cfg/board.cfg"
#define DEFAULT_CONFERENCE_CONFIG_PATH  "/home/<USER>/cfg/conferece.cfg"

// 网络设置文件路径
#define DEFAULT_NETWORK_SETTING_PATH    "/etc/network-setting"
#define DEFAULT_ETH0_SETTING_PATH       "/etc/eth0-setting"
#define DEFAULT_WLAN0_SETTING_PATH      "/etc/wlan0-setting"
#define DEFAULT_3G_SETTING_PATH         "/etc/3g-setting"

// 日志文件路径
#define DEFAULT_LOG_PATH                "/var/log/webcfg.log"
#define DEFAULT_ERROR_LOG_PATH          "/var/log/webcfg_error.log"

// ==================== 配置文件偏移量默认值 ====================

// 基于deprecated/cgi/inc/0define.h的偏移量定义
#define DEFAULT_COMMON_CONFIG_OFFSET    0x1000
#define DEFAULT_BOARD_BASIC_OFFSET      0x2000
#define DEFAULT_CONF_CONFIG_OFFSET      0x3000
#define DEFAULT_CONF_PEER_OFFSET        0x4000
#define DEFAULT_GATEWAY_CONFIG_OFFSET   0x0000
#define DEFAULT_CENTER_CONFIG_OFFSET    0x0000

// 配置文件长度默认值
#define DEFAULT_COMMON_CONFIG_LENGTH    4096
#define DEFAULT_BOARD_CONFIG_LENGTH     4096
#define DEFAULT_CONFERENCE_CONFIG_LENGTH 8192

// ==================== 验证范围默认值 ====================

// 端口范围
#define MIN_PORT_NUMBER                 1024
#define MAX_PORT_NUMBER                 65535

// ID范围（24位）
#define MIN_24BIT_ID                    0x000001
#define MAX_24BIT_ID                    0xFFFFFE

// 时间范围
#define MIN_BUFFERTIME                  10      // 最小缓冲时间10ms
#define MAX_BUFFERTIME                  10000   // 最大缓冲时间10s
#define MIN_DOWNTIME                    1000    // 最小掉线时间1s
#define MAX_DOWNTIME                    60000   // 最大掉线时间60s
#define MIN_RESETTIME                   1       // 最小重置时间1s
#define MAX_RESETTIME                   300     // 最大重置时间5分钟

// 录音参数范围
#define MIN_SAMPLE_RATE                 8000    // 最小采样率
#define MAX_SAMPLE_RATE                 48000   // 最大采样率
#define MIN_FILE_SIZE                   1       // 最小文件大小1MB
#define MAX_FILE_SIZE                   1024    // 最大文件大小1GB
#define MIN_RECORD_TIME                 1       // 最小录音时间1分钟
#define MAX_RECORD_TIME                 480     // 最大录音时间8小时

// 无线参数范围
#define MIN_FREQUENCY                   300     // 最小频率300MHz
#define MAX_FREQUENCY                   800     // 最大频率800MHz
#define MIN_POWER_LEVEL                 1       // 最小功率级别
#define MAX_POWER_LEVEL                 5       // 最大功率级别
#define MIN_COVERAGE_RADIUS             100     // 最小覆盖半径100m
#define MAX_COVERAGE_RADIUS             10000   // 最大覆盖半径10km

// ==================== 工具宏定义 ====================

/**
 * @brief 检查值是否在指定范围内
 */
#define IS_IN_RANGE(value, min, max)    ((value) >= (min) && (value) <= (max))

/**
 * @brief 检查端口是否有效
 */
#define IS_VALID_PORT(port)             IS_IN_RANGE(port, MIN_PORT_NUMBER, MAX_PORT_NUMBER)

/**
 * @brief 检查24位ID是否有效
 */
#define IS_VALID_24BIT_ID(id)           IS_IN_RANGE(id, MIN_24BIT_ID, MAX_24BIT_ID)

/**
 * @brief 获取24位掩码值
 */
#define GET_24BIT_VALUE(value)          ((value) & 0xFFFFFF)

/**
 * @brief 设置默认字符串值
 */
#define SET_DEFAULT_STRING(dest, src, size) \
    do { \
        strncpy(dest, src, size - 1); \
        dest[size - 1] = '\0'; \
    } while(0)

/**
 * @brief 设置默认IP地址
 */
#define SET_DEFAULT_IP(dest, src) \
    SET_DEFAULT_STRING(dest, src, 16)

/**
 * @brief 设置默认MAC地址
 */
#define SET_DEFAULT_MAC(dest, src) \
    SET_DEFAULT_STRING(dest, src, 18)

// ==================== 初始化宏 ====================

/**
 * @brief 初始化网络配置默认值
 */
#define INIT_NETWORK_DEFAULTS(config) \
    do { \
        SET_DEFAULT_IP((config)->ip, DEFAULT_LOCAL_IP); \
        SET_DEFAULT_IP((config)->gateway, DEFAULT_GATEWAY_IP); \
        SET_DEFAULT_IP((config)->netmask, DEFAULT_NETMASK); \
        SET_DEFAULT_IP((config)->dns, DEFAULT_DNS_PRIMARY); \
        SET_DEFAULT_MAC((config)->mac, DEFAULT_MAC_ADDRESS); \
    } while(0)

/**
 * @brief 初始化端口配置默认值
 */
#define INIT_PORT_DEFAULTS(config) \
    do { \
        (config)->voice_port = DEFAULT_VOICE_PORT; \
        (config)->data_listen_port = DEFAULT_DATA_LISTEN_PORT; \
        (config)->data_send_port = DEFAULT_DATA_SEND_PORT; \
        (config)->daemon_port = DEFAULT_DAEMON_PORT; \
        (config)->log_port = DEFAULT_LOG_PORT; \
    } while(0)

/**
 * @brief 初始化设备ID默认值
 */
#define INIT_DEVICE_ID_DEFAULTS(config, device_id) \
    do { \
        (config)->net_address = device_id; \
        (config)->device_id = device_id; \
    } while(0)

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_DEFAULTS_H */ 