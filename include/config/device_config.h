#ifndef WEBCFG_DEVICE_CONFIG_H
#define WEBCFG_DEVICE_CONFIG_H

#include <stdint.h>
#include <stddef.h>
#include <cJSON.h>

// 设备配置模块头文件 - 第二阶段实现

// 呼叫中心配置JSON结构（API交互）
typedef struct {
    uint32_t center_no;           // 中心号码（24位）
    uint32_t center_outssi;       // 中心台统一外部号
    uint32_t center_inssi;        // 中心台统一内应号
    uint8_t vchan_sum;           // 语音通道数
    uint16_t center_voice_port;   // 中心席位语音起始端口
    uint16_t listen_agent_port;   // 监听代理端口
    uint8_t peer_net_type;       // 对等网络类型
    char send_all_agent_ip[16];  // 发送所有代理IP（字符串格式）
    uint16_t send_to_agent_port; // 发送到代理端口
    uint16_t inssi_num;          // 内应号个数
    uint16_t spec_function;      // 特殊功能
} center_config_json_t;

// 呼叫中心配置二进制结构（与原有配置文件兼容）
typedef struct {
    uint32_t center_no:24;
    uint32_t center_outssi:24;   // 中心台统一外部号
    uint32_t center_inssi:24;    // 中心台统一内应号
    uint8_t vchan_sum;
    uint16_t center_voice_port;  // 中心席位语音起始端口
    uint16_t listen_agent_port;
    uint8_t peer_net_type;
    uint32_t send_all_agent_ip;
    uint16_t send_to_agent_port; // peer接收端口
    uint16_t inssi_num;          // 内应号个数
    uint16_t spec_function;
} __attribute__((packed)) center_config_binary_t;

// 配置文件常量定义
#define CALLCENTERCFG "/home/<USER>/cfg/callcenter.cfg"
#define START_ADDR_CALLCENTER 0

// 呼叫中心配置相关函数

/**
 * 验证呼叫中心配置数据
 * @param config JSON配置数据
 * @return 0成功，-1失败
 */
int validate_center_config(const center_config_json_t *config);

/**
 * JSON格式转换为二进制配置
 * @param json_config JSON配置结构
 * @param binary_config 二进制配置结构
 * @return 0成功，-1失败
 */
int convert_center_json_to_binary(const center_config_json_t *json_config, 
                                center_config_binary_t *binary_config);

/**
 * 二进制配置转换为JSON格式
 * @param binary_config 二进制配置结构
 * @param json_config JSON配置结构
 * @return 0成功，-1失败
 */
int convert_center_binary_to_json(const center_config_binary_t *binary_config, 
                                 center_config_json_t *json_config);

/**
 * 读取呼叫中心配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int read_center_config_file(center_config_binary_t *config);

/**
 * 写入呼叫中心配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int write_center_config_file(const center_config_binary_t *config);

/**
 * 设置呼叫中心配置默认值
 * @param config 配置结构指针
 */
void set_default_center_config(center_config_binary_t *config);

// IP地址字符串转换函数
/**
 * IP地址数字转字符串
 * @param ip_num 网络字节序IP地址
 * @param ip_str IP地址字符串缓冲区
 * @param size 缓冲区大小
 */
void ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size);

/**
 * IP地址字符串转数字
 * @param ip_str IP地址字符串
 * @return 网络字节序IP地址
 */
uint32_t ip_str_to_num(const char *ip_str);

// 基站配置模块 - 第二阶段实现

// 设备类型枚举
typedef enum {
    STATION_TYPE_3G = 0,    // 3G基站模块
    STATION_TYPE_4G = 1,    // 4G基站模块
    STATION_TYPE_MAX
} station_type_t;

// 基站配置JSON结构（API交互）
typedef struct {
    uint32_t station_id;          // 基站ID（24位）
    uint8_t station_type;         // 基站类型（3G/4G）
    uint32_t center_ip;           // 中心服务器IP
    uint16_t center_port;         // 中心服务器端口
    uint32_t local_ip;            // 本地IP地址
    uint16_t local_port;          // 本地端口
    char station_name[32];        // 基站名称
    uint8_t signal_strength;      // 信号强度等级（0-10）
    uint8_t voice_codec;          // 语音编码类型
    uint16_t heartbeat_interval;  // 心跳间隔（秒）
    uint8_t auto_register;        // 自动注册开关
    uint32_t group_id;            // 组ID（24位）
    uint16_t frequency_band;      // 频段设置
    uint8_t power_level;          // 功率等级
    char module_version[16];      // 模块版本号
    uint8_t network_mode;         // 网络模式（0=GSM, 1=WCDMA, 2=LTE）
} station_config_json_t;

// 基站配置二进制结构（与原有配置文件兼容）
typedef struct {
    uint32_t station_id:24;       // 基站ID（24位）
    uint8_t station_type;         // 基站类型
    uint32_t center_ip;           // 中心服务器IP
    uint16_t center_port;         // 中心服务器端口
    uint32_t local_ip;            // 本地IP地址
    uint16_t local_port;          // 本地端口
    char station_name[32];        // 基站名称
    uint8_t signal_strength;      // 信号强度等级
    uint8_t voice_codec;          // 语音编码类型
    uint16_t heartbeat_interval;  // 心跳间隔
    uint8_t auto_register;        // 自动注册开关
    uint32_t group_id:24;         // 组ID（24位）
    uint16_t frequency_band;      // 频段设置
    uint8_t power_level;          // 功率等级
    char module_version[16];      // 模块版本号
    uint8_t network_mode;         // 网络模式
} __attribute__((packed)) station_config_binary_t;

// 基站配置文件常量定义
#define STATIONCFG "/home/<USER>/cfg/sci.cfg"
#define START_ADDR_STATION 0

// 基站配置相关函数

/**
 * 检测设备类型（3G/4G）
 * @return station_type_t 设备类型
 */
station_type_t detect_station_type(void);

/**
 * 验证基站配置数据
 * @param config JSON配置数据
 * @return 0成功，-1失败
 */
int validate_station_config(const station_config_json_t *config);

/**
 * JSON格式转换为二进制配置
 * @param json_config JSON配置结构
 * @param binary_config 二进制配置结构
 * @return 0成功，-1失败
 */
int convert_station_json_to_binary(const station_config_json_t *json_config, 
                                  station_config_binary_t *binary_config);

/**
 * 二进制配置转换为JSON格式
 * @param binary_config 二进制配置结构
 * @param json_config JSON配置结构
 * @return 0成功，-1失败
 */
int convert_station_binary_to_json(const station_config_binary_t *binary_config, 
                                  station_config_json_t *json_config);

/**
 * 读取基站配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int read_station_config_file(station_config_binary_t *config);

/**
 * 写入基站配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int write_station_config_file(const station_config_binary_t *config);

/**
 * 设置基站配置默认值
 * @param config 配置结构指针
 * @param station_type 基站类型
 */
void set_default_station_config(station_config_binary_t *config, station_type_t station_type);

/**
 * 获取基站类型字符串
 * @param station_type 基站类型
 * @return 类型字符串
 */
const char* get_station_type_string(station_type_t station_type);

// 交换机配置模块 - 第二阶段实现

// 板卡类型枚举
typedef enum {
    BOARD_TYPE_UNKNOWN = 0,     // 未知板卡
    BOARD_TYPE_IP_SWITCH = 1,   // IP交换机板卡
    BOARD_TYPE_4G_MODULE = 2,   // 4G模块板卡
    BOARD_TYPE_VOICE = 3,       // 语音处理板卡
    BOARD_TYPE_MAX
} board_type_t;

// 交换机配置JSON结构（API交互）
typedef struct {
    uint32_t switch_id;           // 交换机ID（24位）
    uint8_t board_type;           // 板卡类型
    char switch_name[32];         // 交换机名称
    uint32_t center_ip;           // 中心服务器IP
    uint16_t center_port;         // 中心服务器端口
    uint32_t local_ip;            // 本地IP地址
    uint16_t local_port;          // 本地端口
    uint8_t channel_count;        // 通道数量
    uint16_t voice_start_port;    // 语音起始端口
    uint16_t data_start_port;     // 数据起始端口
    uint8_t switch_mode;          // 交换模式（0=透明, 1=半透明, 2=不透明）
    uint16_t timeout_interval;    // 超时间隔（秒）
    uint8_t priority_level;       // 优先级等级（0-7）
    uint8_t qos_enabled;          // QoS开关
    char firmware_version[16];    // 固件版本号
    uint8_t auto_failover;        // 自动故障切换
    uint32_t backup_server_ip;    // 备份服务器IP
    uint16_t backup_server_port;  // 备份服务器端口
} switch_config_json_t;

// 交换机配置二进制结构（与原有配置文件兼容）
typedef struct {
    uint32_t switch_id:24;        // 交换机ID（24位）
    uint8_t board_type;           // 板卡类型
    char switch_name[32];         // 交换机名称
    uint32_t center_ip;           // 中心服务器IP
    uint16_t center_port;         // 中心服务器端口
    uint32_t local_ip;            // 本地IP地址
    uint16_t local_port;          // 本地端口
    uint8_t channel_count;        // 通道数量
    uint16_t voice_start_port;    // 语音起始端口
    uint16_t data_start_port;     // 数据起始端口
    uint8_t switch_mode;          // 交换模式
    uint16_t timeout_interval;    // 超时间隔
    uint8_t priority_level;       // 优先级等级
    uint8_t qos_enabled;          // QoS开关
    char firmware_version[16];    // 固件版本号
    uint8_t auto_failover;        // 自动故障切换
    uint32_t backup_server_ip;    // 备份服务器IP
    uint16_t backup_server_port;  // 备份服务器端口
} __attribute__((packed)) switch_config_binary_t;

// 交换机配置文件常量定义
#define SWITCHCFG "/home/<USER>/cfg/board.cfg"
#define START_ADDR_SWITCH 0

// 板卡信息结构
typedef struct {
    board_type_t board_type;      // 板卡类型
    char board_name[32];          // 板卡名称
    char board_version[16];       // 板卡版本
    uint8_t channel_count;        // 支持的通道数
    uint32_t capabilities;        // 板卡能力位图
} board_info_t;

// 交换机配置相关函数

/**
 * 检测板卡类型
 * @param board_info 输出板卡信息
 * @return 0成功，-1失败
 */
// 移除了detect_board_type函数声明，因为旧项目中不存在此功能

/**
 * 验证交换机配置数据
 * @param config JSON配置数据
 * @return 0成功，-1失败
 */
int validate_switch_config(const switch_config_json_t *config);

/**
 * JSON格式转换为二进制配置
 * @param json_config JSON配置结构
 * @param binary_config 二进制配置结构
 * @return 0成功，-1失败
 */
int convert_switch_json_to_binary(const switch_config_json_t *json_config, 
                                 switch_config_binary_t *binary_config);

/**
 * 二进制配置转换为JSON格式
 * @param binary_config 二进制配置结构
 * @param json_config JSON配置结构
 * @return 0成功，-1失败
 */
int convert_switch_binary_to_json(const switch_config_binary_t *binary_config, 
                                 switch_config_json_t *json_config);

/**
 * 读取交换机配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int read_switch_config_file(switch_config_binary_t *config);

/**
 * 写入交换机配置文件
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int write_switch_config_file(const switch_config_binary_t *config);

/**
 * 设置交换机配置默认值
 * @param config 配置结构指针
 * @param board_type 板卡类型
 */
void set_default_switch_config(switch_config_binary_t *config, board_type_t board_type);

/**
 * 获取板卡类型字符串
 * @param board_type 板卡类型
 * @return 类型字符串
 */
const char* get_board_type_string(board_type_t board_type);

/**
 * 获取交换模式字符串
 * @param switch_mode 交换模式
 * @return 模式字符串
 */
const char* get_switch_mode_string(uint8_t switch_mode);

#endif /* WEBCFG_DEVICE_CONFIG_H */ 