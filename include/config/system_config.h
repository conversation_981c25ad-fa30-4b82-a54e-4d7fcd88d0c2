/**
 * @file system_config.h
 * @brief 系统管理模块配置头文件 - 严格基于旧项目功能一致性
 * <AUTHOR> Team
 * @date 2024
 * 
 * 严格基于deprecated/cgi/0system.c、0passwd.c、0ntp.c、0down.c
 * 不增加任何旧项目中不存在的功能
 */

#ifndef WEBCFG_SYSTEM_CONFIG_H
#define WEBCFG_SYSTEM_CONFIG_H

#include <stdint.h>
#include <stdio.h>
#include <time.h>
#include "config/common/config_base.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 系统管理模块（基于0system.c） ====================

/**
 * 系统操作类型枚举 - 基于旧项目0system.c
 */
typedef enum {
    SYSTEM_OP_NONE = 0,         // 无操作
    SYSTEM_OP_UPDATE = 1,       // 系统升级
    SYSTEM_OP_REBOOT = 2,       // 系统重启
    SYSTEM_OP_RESET_CFG = 3     // 重置配置
} system_operation_t;

// ==================== 用户管理模块（基于0passwd.c） ====================

/**
 * 用户管理操作结果 - 基于旧项目0passwd.c
 */
typedef enum {
    USER_OP_SUCCESS = 0,                // 操作成功
    USER_OP_INVALID_PARAM = -1,         // 参数无效
    USER_OP_PASSWORD_MISMATCH = -2,     // 密码不匹配
    USER_OP_PASSWORD_TOO_SHORT = -3,    // 密码太短
    USER_OP_PASSWORD_TOO_LONG = -4,     // 密码太长
    USER_OP_FILE_ERROR = -5,            // 文件操作错误
    USER_OP_PERMISSION_DENIED = -6      // 权限拒绝
} user_operation_result_t;

/**
 * 密码修改请求结构 - 基于旧项目0passwd.c
 */
typedef struct {
    char old_password[32];              // 旧密码
    char new_password1[32];             // 新密码
    char new_password2[32];             // 确认密码
} password_change_request_t;

// ==================== NTP时间同步模块（基于0ntp.c） ====================

/**
 * NTP配置结构 - 基于旧项目0ntp.c
 */
typedef struct {
    uint8_t ntp_enabled;                // NTP使能(0=禁用,1=启用)
    uint8_t ntp_client_mode;            // 客户端模式(0=禁用,1=启用)
    uint8_t ntp_server_mode;            // 服务器模式(0=禁用,1=启用)
    char ntp_server[64];                // NTP服务器地址
    uint16_t ntp_port;                  // NTP端口(默认123)
    uint32_t sync_interval;             // 同步间隔(秒)
    uint8_t timezone_offset;            // 时区偏移(小时)
} ntp_config_t;

// ==================== 日志显示模块（基于0down.c） ====================

/**
 * 日志类型枚举 - 基于旧项目0down.c
 */
typedef enum {
    LOG_TYPE_STARTUP = 0,               // 启动日志
    LOG_TYPE_SIGNAL = 1,                // 信号质量日志
    LOG_TYPE_3G_INFO = 2,               // 3G网络信息
    LOG_TYPE_3G_CONNECTION = 3,         // 3G连接日志
    LOG_TYPE_WLAN = 4,                  // WLAN连接日志
    LOG_TYPE_SYSTEM = 5,                // 系统日志
    LOG_TYPE_ERROR = 6                  // 错误日志
} log_type_t;

/**
 * 信号质量结构 - 基于旧项目0down.c信号检测功能
 */
typedef struct {
    char rssi[8];                       // 信号强度指示
    char ber[8];                        // 误码率
    char dbm[32];                       // 信号强度(dBm)
    char error_message[64];             // 错误信息
    time_t detection_time;              // 检测时间
} signal_quality_t;

// ==================== 配置文件路径定义 ====================

// NTP配置文件存储路径
#define NTP_CONFIG_STORAGE      "/home/<USER>/cfg/ntp.cfg"

// ==================== 系统管理函数 ====================

/**
 * 执行系统操作 - 基于旧项目0system.c
 */
config_result_t system_execute_operation(system_operation_t operation);

/**
 * 系统重启 - 基于旧项目0system.c
 */
config_result_t system_reboot(void);

/**
 * 重置配置 - 基于旧项目0system.c
 */
config_result_t system_reset_config(void);

// ==================== 用户管理函数 ====================

/**
 * 修改密码 - 基于旧项目0passwd.c
 */
user_operation_result_t user_change_password(const char *username, 
                                            const password_change_request_t *request);

/**
 * 验证用户密码 - 基于旧项目简单验证
 */
int user_verify_password(const char *username, const char *password);

// ==================== NTP时间同步函数 ====================

/**
 * 读取NTP配置 - 基于旧项目0ntp.c
 */
config_result_t ntp_read_config(ntp_config_t *config);

/**
 * 保存NTP配置 - 基于旧项目0ntp.c
 */
config_result_t ntp_write_config(const ntp_config_t *config);

/**
 * 执行时间同步 - 基于旧项目0ntp.c
 */
config_result_t ntp_sync_time(void);

/**
 * 验证NTP配置 - 基于旧项目0ntp.c
 */
config_result_t ntp_validate_config(const ntp_config_t *config);

// ==================== 日志显示函数 ====================

/**
 * 获取信号质量 - 基于旧项目0down.c
 */
config_result_t log_get_signal_quality(signal_quality_t *quality);

/**
 * 读取日志文件 - 基于旧项目0down.c
 */
config_result_t log_read_file(log_type_t log_type, char *buffer, size_t buffer_size);

#ifdef __cplusplus
}
#endif

#endif /* WEBCFG_SYSTEM_CONFIG_H */ 