/*******************************************************************
 * File          : recorder_base_config.h
 * Author        : WebCfg Refactor Team
 * Created       : 2024-12-26
 * Last modified : 2025-01-01
 *------------------------------------------------------------------
 * Description :
 * 录音基站配置模块头文件 - 兼容性重定向
 * 重定向到新的模块化结构 src/config/recorder/recorder_config.h
 *------------------------------------------------------------------
 * Modification history :
 * 2024-12-26 : 基于旧项目实际结构重新创建
 * 2025-01-01 : 重构为模块化结构，此文件作为兼容性重定向
 *******************************************************************/

#ifndef __RECORDER_BASE_CONFIG_H__
#define __RECORDER_BASE_CONFIG_H__

// 重定向到新的模块化结构
#include "recorder/recorder_config.h"

// 兼容性类型别名
typedef recorder_device_type_t recorder_base_device_type_t;
typedef recorder_net_config_t st_net_config_t;
typedef recorder_board_net_t st_board_net_t;
typedef recorder_cfg_net_t st_cfg_net_t;
typedef recorder_cfg_common_t st_cfg_common_t;
typedef recorder_cfg_board_basic_t st_cfg_board_basic_t;
typedef recorder_cfg_conf_t st_cfg_conf_t;
typedef recorder_cfg_peer_base_t st_cfg_peer_base_t;
typedef recorder_config_binary_t recorder_base_config_t;
typedef recorder_config_json_t recorder_base_config_json_t;

// 兼容性常量别名
#define DEVICE_TYPE_RECORDER RECORDER_DEVICE_TYPE_RECORDER
#define DEVICE_TYPE_MINI RECORDER_DEVICE_TYPE_MINI

// 兼容性函数别名
#define validate_recorder_base_config validate_recorder_config
#define convert_recorder_base_json_to_binary convert_recorder_json_to_binary
#define convert_recorder_base_binary_to_json convert_recorder_binary_to_json
#define read_recorder_base_config_file read_recorder_config_file
#define write_recorder_base_config_file write_recorder_config_file
#define set_default_recorder_base_config set_default_recorder_config

// API函数别名
#define handle_recorder_base_config_get recorder_handle_config_get
#define handle_recorder_base_config_post recorder_handle_config_post

#endif /* __RECORDER_BASE_CONFIG_H__ */