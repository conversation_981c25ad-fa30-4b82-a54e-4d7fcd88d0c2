#ifndef WEBCFG_CONFIG_DEFAULTS_H
#define WEBCFG_CONFIG_DEFAULTS_H

#include <stdint.h>

///////////////////////////////////////////////////////////////////////////////
// 网络相关默认值 (基于deprecated模块)
///////////////////////////////////////////////////////////////////////////////

// 默认IP地址和网络配置
#define DEFAULT_VOICE_IP                "*************"
#define DEFAULT_GATEWAY_IP              "***********"
#define DEFAULT_NETMASK                 "*************"
#define DEFAULT_DNS_SERVER              "*******"

///////////////////////////////////////////////////////////////////////////////
// 设备ID默认值 (与deprecated模块100%一致)
///////////////////////////////////////////////////////////////////////////////

// 24位ID默认值
#define DEFAULT_CENTER_NO               0x000001
#define DEFAULT_NET_ADDRESS             0x000001
#define DEFAULT_GATEWAY_NO              0x000001
#define DEFAULT_STATION_ID              0x000001

// 24位ID范围验证
#define MIN_24BIT_ID                    0x000001
#define MAX_24BIT_ID                    0xFFFFFE

///////////////////////////////////////////////////////////////////////////////
// 端口号默认值和范围 (与deprecated模块100%一致)
///////////////////////////////////////////////////////////////////////////////

// 端口范围
#define MIN_PORT_NUMBER                 1024
#define MAX_PORT_NUMBER                 65535

// 会议模块端口 (基于deprecated/cgi/inc/0define.h)
#define CONFERENCE_RECV_NET_PORT        2600
#define CONFERENCE_RECV_BUS_PORT        2601
#define CONFERENCE_SEND_NET_PORT        2602

// 总线模块端口
#define BUS_RECV_CONFERENCE_PORT        2603
#define BUS_SEND_CONFERENCE_PORT        2604

// 呼叫中心端口
#define CALLCENTER_RECV_PORT            2700    // 呼叫中心接收数据端口
#define CALLCENTER_SEND_PORT            2701
#define CALLCENTER_RECV_AGENT_PORT      2702    // 呼叫中心席位数据端口
#define CALLCENTER_SEND_AGENT_PORT      2703
#define CALLCENTER_AGENT_RECV_PORT      2704    // 发送到席位的目标端口

// 语音端口
#define VOICE_PORT_BASE                 7000
#define FUNCTION_VOICE_SWITCH_PORT_BASE 2950    // 交换机与语音模块语音端口
#define FUNCTION_VOICE_PEER_PORT_BASE   2800

// 系统模块端口
#define UNKNOWN_MODULE_RECV_PORT        8998
#define UNKNOWN_MODULE_SEND_PORT        8999

#define DAEMON_MODULE_RECV_PORT         9000
#define DAEMON_MODULE_SEND_PORT         9001

#define LOG_MODULE_RECV_PORT            9002
#define LOG_MODULE_SEND_PORT            9003
#define LOG_MODULE_LOG_PORT             8002
#define LOG_MODULE_STATUS_PORT          8003

#define CONFIGURE_MODULE_RECV_PORT      9004
#define CONFIGURE_MODULE_SEND_PORT      9005

#define BUS_MODULE_RECV_PORT            9006
#define BUS_MODULE_SEND_PORT            9007

#define CONFERENCE_MODULE_RECV_PORT     9008
#define CONFERENCE_MODULE_SEND_PORT     9009

#define CALLCENTER_MODULE_RECV_PORT     9010
#define CALLCENTER_MODULE_SEND_PORT     9011

// 默认端口
#define DEFAULT_VOICE_PORT              3000
#define DEFAULT_DATA_LISTEN_PORT        2800
#define DEFAULT_VBUS_BASE_PORT          4000
#define DEFAULT_PEER_DATA_PORT          2850
#define DEFAULT_PEER_VOICE_PORT         3050
#define DEFAULT_PEER_NET_ADDRESS        0x000002

///////////////////////////////////////////////////////////////////////////////
// 设备类型枚举 (与deprecated模块100%一致)
///////////////////////////////////////////////////////////////////////////////

// enumBaseType
#define DEVICE_TYPE_SWITCH              0x10
#define DEVICE_TYPE_BASE                0x11
#define DEVICE_TYPE_CALLCENTER          0x12
#define DEVICE_TYPE_RECORDER            0x13
#define DEVICE_TYPE_LINK                0x14
#define DEVICE_TYPE_MPT                 0x15
#define DEVICE_TYPE_PDT                 0x16
#define DEVICE_TYPE_MINI                0x17
#define DEVICE_TYPE_3G                  0x18
#define DEVICE_TYPE_UNKNOWN             0x1F

// enumPeerNetType
#define PEER_NET_UNICAST                0    // 单播
#define PEER_NET_BROADCAST              1    // broadcast
#define PEER_NET_MULTICAST              2    // multicast

///////////////////////////////////////////////////////////////////////////////
// 系统配置默认值
///////////////////////////////////////////////////////////////////////////////

// 语音通道数量 (基于deprecated中MAX_VOCODER_CHAN, MAX_NOVOCODER_CHAN)
#define MAX_VOCODER_CHAN                16      // 有声码器
#define MAX_NOVOCODER_CHAN              64      // 无声码器
#define DEFAULT_VCHAN_SUM               10

// 虚拟CAN数量
#define VCAN_MAX                        4   // 基站端的语音CAN数量

// 对端基站数量
#define PEER_MAX                        8   // 对端基站数

// 时间相关默认值
#define DEFAULT_HEARTBEAT_INTERVAL      30      // 心跳间隔(秒)
#define DEFAULT_BUFFER_TIME             100     // 缓冲时间(ms)
#define DEFAULT_VOICE_DOWN_TIME         300     // 语音结束时间(ms)

// 日志相关
#define DEFAULT_LOG_MAX_SIZE            (1024*1024)  // 1MB
#define DEFAULT_CONFIG_BACKUP_COUNT     5

// 频率相关 (deprecated中CFG_FREQ_SUM)
#define CFG_FREQ_SUM                    16      // 频点数量

///////////////////////////////////////////////////////////////////////////////
// 验证宏定义
///////////////////////////////////////////////////////////////////////////////

#define IS_VALID_PORT(port)             ((port) >= MIN_PORT_NUMBER && (port) <= MAX_PORT_NUMBER)
#define IS_VALID_24BIT_ID(id)           ((id) >= MIN_24BIT_ID && (id) <= MAX_24BIT_ID)
#define GET_24BIT_VALUE(value)          ((value) & 0xFFFFFF)
#define IS_VALID_DEVICE_TYPE(type)      (((type) >= DEVICE_TYPE_SWITCH && (type) <= DEVICE_TYPE_3G) || (type) == DEVICE_TYPE_UNKNOWN)
#define IS_VALID_PEER_NET_TYPE(type)    ((type) >= PEER_NET_UNICAST && (type) <= PEER_NET_MULTICAST)

///////////////////////////////////////////////////////////////////////////////
// 工具宏
///////////////////////////////////////////////////////////////////////////////

#define ARRAY_SIZE(arr)                 (sizeof(arr) / sizeof((arr)[0]))
#define SAFE_STRCPY(dst, src)           strncpy(dst, src, sizeof(dst) - 1); (dst)[sizeof(dst) - 1] = '\0'

// 字节序转换
#define HTONL_SAFE(x)                   htonl(x)
#define NTOHL_SAFE(x)                   ntohl(x)
#define HTONS_SAFE(x)                   htons(x)
#define NTOHS_SAFE(x)                   ntohs(x)

// 位操作宏 (deprecated模块中使用)
#define SETBIT(var, bit)                ((var) |= (bit))
#define CLEARBIT(var, bit)              ((var) &= ~(bit))
#define TESTBIT(var, bit)               ((var) & (bit))

///////////////////////////////////////////////////////////////////////////////
// 错误码定义 (与deprecated中checkRET兼容)
///////////////////////////////////////////////////////////////////////////////

#define READ_SUCCESS                    0
#define SAVE_SUCCESS                    1
#define OPEN_ETH_ERR                    (1 << 1)
#define OPEN_BASIC_ERR                  (1 << 11)
#define OPEN_3G_ERR                     (1 << 12)
#define OPEN_NET_ERR                    (1 << 13)
#define OPEN_COMMON_ERR                 (1 << 14)
#define OPEN_CONF_ERR                   (1 << 15)
#define OPEN_CENTER_ERR                 (1 << 16)
#define OPEN_VOCODER_ERR                (1 << 17)
#define OPEN_BASE_ERR                   (1 << 18)

///////////////////////////////////////////////////////////////////////////////
// 配置键名定义 (与deprecated web form keys 100%一致)
///////////////////////////////////////////////////////////////////////////////

// common.cfg keys
#define COMMON_KEY_DS                   "COMMON_DS"
#define COMMON_KEY_SW                   "COMMON_SW"
#define COMMON_KEY_CONFNUM              "COMMON_CONF_NUM"
#define COMMON_KEY_CONVNUM              "COMMON_CONV_NUM"

// board.cfg keys
#define BASIC_KEY_LOGIP                 "BASIC_LOGIP"
#define BASIC_KEY_LOGPORT               "BASIC_LOGPORT"

// conference.cfg keys
#define CONF_KEY_WORK_MODE              "CONF_WORK_MODE"
#define CONF_KEY_SPEC_FUNC              "CONF_SPEC_FUNC"
#define CONF_KEY_PEER_NUM               "CONF_PEER_NUM"
#define CONF_KEY_VOICE_IP               "CONF_VOICE_IP"
#define CONF_KEY_VBUS_PORT              "CONF_VBUS_PORT"
#define CONF_KEY_VCHAN_NUM              "CONF_VCHAN_NUM"
#define CONF_KEY_BUFFER_TIME            "CONF_BUFFER_TIME"
#define CONF_KEY_VOICE_DOWN_TIME        "CONF_VOICE_DOWN_TIME"

// peer keys
#define PEER_KEY_IP                     "PEER_IP"
#define PEER_KEY_VIP                    "PEER_VIP"
#define PEER_KEY_DPORT                  "PEER_DPORT"
#define PEER_KEY_VPORT                  "PEER_VPORT"
#define PEER_KEY_ADDR_DS                "PEER_DS"
#define PEER_KEY_ADDR_SW                "PEER_SW"
#define PEER_KEY_ADDR_BS                "PEER_BS"
#define PEER_KEY_TYPE                   "PEER_TYPE"

// sci.cfg keys
#define SCI_KEY_GET_METHOD              "SCI_GET_METHOD"
#define SCI_KEY_NET_MODE                "SCI_NET_MODE"
#define SCI_KEY_VOICE_IP                "SCI_VOICE_IP"
#define SCI_KEY_DATA_PORT               "SCI_DATA_PORT"
#define SCI_KEY_VOICE_PORT              "SCI_VOICE_PORT"
#define SCI_KEY_BASE_TYPE               "SCI_BASE_TYPE"
#define SCI_KEY_BUFFER_TIME             "SCI_BUFFER_TIME"
#define SCI_KEY_VOICE_DOWN_TIME         "SCI_VOICE_DOWN_TIME"
#define SCI_KEY_RESET_TIME              "SCI_RESET_TIME"

// callcenter.cfg keys
#define CENTER_KEY_DS                   "CENTER_DS"
#define CENTER_KEY_SW                   "CENTER_SW"
#define CENTER_KEY_BS                   "CENTER_BS"
#define CENTER_KEY_OUTSSI               "CENTER_OUTSSI"
#define CENTER_KEY_INSSI                "CENTER_INSSI"
#define CENTER_KEY_INSSISUM             "CENTER_INSSISUM"
#define CENTER_KEY_VCHANSUM             "CENTER_VCHANSUM"
#define CENTER_KEY_VPORT                "CENTER_VPORT"
#define CENTER_KEY_DPORT                "CENTER_DPORT"
#define CENTER_KEY_PEERNET              "CENTER_PEERNET"
#define CENTER_KEY_PEERIP               "CNETER_PEERIP"     // 保持原有拼写错误以确保兼容性
#define CENTER_KEY_PEERPORT             "CENTER_PEERPORT"
#define CENTER_KEY_SPEC_FUNC            "CENTER_SPEC_FUNC"

// vocoder.cfg keys
#define VOCODER_KEY_AMP                 "VOCODER_AMP"
#define VOCODER_KEY_PCMTYPE             "VOCODER_PCMTYPE"
#define VOCODER_KEY_CHANSUM             "VOCODER_CHANSUM"
#define VOCODER_KEY_VTYPE               "VOCODER_VTYPE"
#define VOCODER_KEY_SYSCODE             "VOCODER_SYSCODE"

// base.cfg keys
#define BASE_KEY_SYSCODE                "BASE_SYSCODE"
#define BASE_KEY_FREQNO                 "BASE_FREQNO"

///////////////////////////////////////////////////////////////////////////////
// 默认值获取接口
///////////////////////////////////////////////////////////////////////////////

// 获取设备类型默认配置
static inline uint8_t get_default_device_type(void) {
    return DEVICE_TYPE_SWITCH;
}

// 获取默认端口配置
static inline uint16_t get_default_voice_port(void) {
    return DEFAULT_VOICE_PORT;
}

static inline uint16_t get_default_data_port(void) {
    return DEFAULT_DATA_LISTEN_PORT;
}

// 获取默认24位ID
static inline uint32_t get_default_center_id(void) {
    return DEFAULT_CENTER_NO;
}

// 获取默认时间配置
static inline uint16_t get_default_buffer_time(void) {
    return DEFAULT_BUFFER_TIME;
}

static inline uint16_t get_default_voice_down_time(void) {
    return DEFAULT_VOICE_DOWN_TIME;
}

#endif // WEBCFG_CONFIG_DEFAULTS_H