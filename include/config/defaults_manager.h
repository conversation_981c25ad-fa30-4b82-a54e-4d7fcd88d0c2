#ifndef WEBCFG_CONFIG_DEFAULTS_MANAGER_H
#define WEBCFG_CONFIG_DEFAULTS_MANAGER_H

#include "config/defaults.h"
#include <stdint.h>
#include <stddef.h>

// 默认值设置结果
typedef enum {
    DEFAULTS_SUCCESS = 0,
    DEFAULTS_ERROR_INVALID_ARG = -1,
    DEFAULTS_ERROR_BUFFER_SIZE = -2,
    DEFAULTS_ERROR_NOT_FOUND = -3
} defaults_result_t;

// 默认值类型枚举
typedef enum {
    DEFAULT_TYPE_NETWORK,
    DEFAULT_TYPE_CENTER,
    DEFAULT_TYPE_GATEWAY,
    DEFAULT_TYPE_STATION,
    DEFAULT_TYPE_SYSTEM,
    DEFAULT_TYPE_COUNT
} default_config_type_t;

// 网络默认配置结构
typedef struct {
    char voice_ip[16];
    char gateway_ip[16];
    char netmask[16];
    char dns_server[16];
    uint16_t voice_port;
    uint16_t data_port;
} default_network_config_t;

// 设备默认配置结构
typedef struct {
    uint32_t device_id;
    uint8_t device_type;
    uint16_t voice_port_base;
    uint16_t data_port_base;
    uint8_t peer_net_type;
} default_device_config_t;

// 系统默认配置结构
typedef struct {
    uint16_t heartbeat_interval;
    uint16_t buffer_time;
    uint16_t voice_down_time;
    uint16_t max_vchan_count;
    uint8_t max_peer_count;
} default_system_config_t;

// 默认值管理器接口

// 获取网络默认配置
int defaults_get_network_config(default_network_config_t *config);

// 获取设备默认配置
int defaults_get_device_config(default_config_type_t type, default_device_config_t *config);

// 获取系统默认配置
int defaults_get_system_config(default_system_config_t *config);

// 设置默认IP地址
int defaults_set_default_ip(const char *field_name, const char *ip_address);

// 设置默认端口
int defaults_set_default_port(const char *field_name, uint16_t port);

// 设置默认24位ID
int defaults_set_default_24bit_id(const char *field_name, uint32_t id);

// 验证并应用默认值到数据结构
int defaults_apply_network_defaults(void *network_config, size_t config_size);
int defaults_apply_device_defaults(default_config_type_t type, void *device_config, size_t config_size);
int defaults_apply_system_defaults(void *system_config, size_t config_size);

// 重置所有默认值
int defaults_reset_all(void);

// 获取默认值的字符串表示 (用于调试和日志)
int defaults_get_string_value(const char *field_name, char *buffer, size_t buffer_size);

// 列出所有可用的默认值字段
int defaults_list_all_fields(char **field_names, size_t max_count, size_t *actual_count);

#endif // WEBCFG_CONFIG_DEFAULTS_MANAGER_H 