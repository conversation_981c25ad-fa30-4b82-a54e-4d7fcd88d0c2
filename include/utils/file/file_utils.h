#ifndef WEBCFG_FILE_UTILS_H
#define WEBCFG_FILE_UTILS_H

#include <stddef.h>
#include <stdint.h>

// 错误码定义
#define FILE_OPERATION_SUCCESS      0
#define FILE_OPERATION_ERROR        -1
#define FILE_OPERATION_NOT_FOUND    -2
#define FILE_OPERATION_PERMISSION   -3
#define FILE_OPERATION_NO_SPACE     -4
#define FILE_OPERATION_INVALID_ARG  -5

// 基于偏移量的文件读写操作 (兼容deprecated模块)
int file_read_at_offset(const char *path, long offset, void *data, size_t size);
int file_write_at_offset(const char *path, long offset, const void *data, size_t size);

// 完整文件读写操作
int file_read_full(const char *path, void *data, size_t max_size, size_t *actual_size);
int file_write_full(const char *path, const void *data, size_t size);

// 原子性文件操作
int file_atomic_write(const char *path, const void *data, size_t size);
int file_backup_and_write(const char *path, const void *data, size_t size);

// 文件状态检查
int file_exists(const char *path);
long file_get_size(const char *path);
int file_is_readable(const char *path);
int file_is_writable(const char *path);

// 文件备份和恢复
int file_create_backup(const char *path, const char *backup_suffix);
int file_restore_from_backup(const char *path, const char *backup_suffix);

// 旧接口兼容 (与现有file_utils.c兼容)
int read_file_with_offset(const char *path, long offset, void *data, size_t size);
int write_file_with_offset(const char *path, long offset, const void *data, size_t size);

#endif // WEBCFG_FILE_UTILS_H 