#ifndef WEBCFG_CONFIG_FILE_MANAGER_H
#define WEBCFG_CONFIG_FILE_MANAGER_H

#include <stddef.h>

// 配置文件类型枚举 - 对应deprecated模块中的所有配置文件
typedef enum {
    // 网络配置文件
    CONFIG_TYPE_NETWORK = 0,        // /etc/eth0-setting
    CONFIG_TYPE_NETWORK_SETTING,    // /etc/network-setting  
    CONFIG_TYPE_WLAN,               // /etc/wlan0-setting
    CONFIG_TYPE_3G,                 // /etc/3g-setting
    
    // 设备配置文件
    CONFIG_TYPE_CENTER,             // /home/<USER>/cfg/callcenter.cfg
    CONFIG_TYPE_GATEWAY,            // /home/<USER>/cfg/gateway.cfg  
    CONFIG_TYPE_STATION,            // /home/<USER>/cfg/sci.cfg
    CONFIG_TYPE_SWITCH,             // /home/<USER>/cfg/common.cfg (交换机使用common.cfg)
    CONFIG_TYPE_RECORDER,           // /home/<USER>/cfg/record.cfg
    
    // 系统配置文件
    CONFIG_TYPE_COMMON,             // /home/<USER>/cfg/common.cfg
    CONFIG_TYPE_BOARD,              // /home/<USER>/cfg/board.cfg
    CONFIG_TYPE_CONFERENCE,         // /home/<USER>/cfg/conferece.cfg (保持原有拼写)
    CONFIG_TYPE_VOCODER,            // /home/<USER>/cfg/vocoder.cfg
    CONFIG_TYPE_BASE,               // /home/<USER>/cfg/base.cfg
    CONFIG_TYPE_LINKSWITCH,         // /home/<USER>/cfg/linkswitch.cfg
    CONFIG_TYPE_MPTLINK,            // /home/<USER>/cfg/mptlink.cfg
    
    // 版本文件
    CONFIG_TYPE_VERSION,            // /home/<USER>/cfg/cfgver.cfg
    
    // 日志文件 (系统模块使用)
    CONFIG_TYPE_LOG_STARTUP,        // /tmp/startipsw.log
    CONFIG_TYPE_LOG_NETWORK,        // /tmp/netconfig.log
    CONFIG_TYPE_LOG_WLAN,           // /tmp/wlanconfig.log
    CONFIG_TYPE_LOG_PPP,            // /tmp/pppconfig.log
    
    CONFIG_TYPE_COUNT               // 配置类型数量
} config_type_t;

// 配置文件映射结构
typedef struct {
    config_type_t type;             // 配置类型
    const char *file_path;          // 文件路径
    const char *backup_suffix;      // 备份文件后缀
    int required_permissions;       // 所需权限
} config_file_mapping_t;

// 获取配置文件路径
const char* config_get_file_path(config_type_t type);

// 获取备份文件后缀
const char* config_get_backup_suffix(config_type_t type);

// 获取文件所需权限
int config_get_required_permissions(config_type_t type);

// 确保目录存在
int config_ensure_directory_exists(const char *file_path);

// 验证文件访问权限
int config_file_validate_access(const char *file_path);

// 获取备份文件路径
int config_get_backup_path(config_type_t type, char *backup_path, size_t size);

// 获取所有映射配置
const config_file_mapping_t* config_get_all_mappings(size_t *count);

#endif // WEBCFG_CONFIG_FILE_MANAGER_H 