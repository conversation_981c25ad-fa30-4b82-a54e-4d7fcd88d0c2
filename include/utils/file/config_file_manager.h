#ifndef WEBCFG_CONFIG_FILE_MANAGER_H
#define WEBCFG_CONFIG_FILE_MANAGER_H

#include <stddef.h>
#include "config/common/config_base.h"

// 注意：此文件使用 config_base.h 中定义的 config_type_t 枚举
// 不重复定义枚举类型，避免冲突

// 配置文件映射结构
typedef struct {
    config_type_t type;             // 配置类型
    const char *file_path;          // 文件路径
    const char *backup_suffix;      // 备份文件后缀
    int required_permissions;       // 所需权限
} config_file_mapping_t;

// 获取配置文件路径
const char* config_get_file_path(config_type_t type);

// 获取备份文件后缀
const char* config_get_backup_suffix(config_type_t type);

// 获取文件所需权限
int config_get_required_permissions(config_type_t type);

// 确保目录存在
int config_ensure_directory_exists(const char *file_path);

// 验证文件访问权限
int config_file_validate_access(const char *file_path);

// 获取备份文件路径
int config_get_backup_path(config_type_t type, char *backup_path, size_t size);

// 获取所有映射配置
const config_file_mapping_t* config_get_all_mappings(size_t *count);

#endif // WEBCFG_CONFIG_FILE_MANAGER_H 