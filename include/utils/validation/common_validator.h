#ifndef WEBCFG_COMMON_VALIDATOR_H
#define WEBCFG_COMMON_VALIDATOR_H

#include <stdint.h>
#include <stddef.h>

// 验证错误码定义
#define VALIDATION_SUCCESS              0
#define VALIDATION_ERROR_INVALID_ARG    -1
#define VALIDATION_ERROR_RANGE_INVALID  -2
#define VALIDATION_ERROR_FORMAT_INVALID -3
#define VALIDATION_ERROR_LENGTH_INVALID -4
#define VALIDATION_ERROR_IP_INVALID     -5
#define VALIDATION_ERROR_PORT_INVALID   -6

// 24位ID验证 (与deprecated模块兼容：0x000001-0xFFFFFE)
int validate_24bit_id(uint32_t id);
uint32_t get_24bit_value(uint32_t raw_value);
int validate_center_id(uint32_t center_id);
int validate_gateway_id(uint32_t gateway_id);
int validate_station_id(uint32_t station_id);

// 端口号验证 (与deprecated模块兼容：1024-65535)
int validate_port_number(uint16_t port);
int validate_voice_port(uint16_t port);
int validate_data_port(uint16_t port);
int validate_listen_port(uint16_t port);

// 字符串长度验证
int validate_string_length(const char *str, size_t min_len, size_t max_len);
int validate_string_not_empty(const char *str);

// IP地址验证 (统一接口，替代直接使用inet_aton/inet_addr)
int ip_validate_address(const char *ip_str);
int ip_validate_netmask(const char *mask_str);
int ip_validate_range(const char *ip_str, const char *min_ip, const char *max_ip);
int ip_validate_in_subnet(const char *ip, const char *subnet, const char *mask);
int ip_validate_private(const char *ip_str);
int ip_validate_not_reserved(const char *ip_str);

// 数值范围验证
int validate_uint8_range(uint8_t value, uint8_t min_val, uint8_t max_val);
int validate_uint16_range(uint16_t value, uint16_t min_val, uint16_t max_val);
int validate_uint32_range(uint32_t value, uint32_t min_val, uint32_t max_val);

// 配置项验证 (基于deprecated模块的业务逻辑)
int validate_voice_channel_count(uint16_t vchan_sum);
int validate_buffer_time(uint16_t buffer_time);
int validate_voice_down_time(uint16_t down_time);
int validate_peer_base_num(uint8_t peer_num);

// MAC地址验证
int validate_mac_bytes(const uint8_t mac[6]);
int validate_mac_string(const char *mac_str);

// 系统代码验证 (4位16进制)
int validate_system_code(uint16_t syscode);

// 频点验证 (4位16进制)
int validate_frequency(uint16_t freq);

// 网络模式验证
int validate_network_mode(uint8_t mode);

// 设备类型验证
int validate_device_type(uint8_t type);

#endif // WEBCFG_COMMON_VALIDATOR_H 