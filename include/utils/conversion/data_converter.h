#ifndef WEBCFG_DATA_CONVERTER_H
#define WEBCFG_DATA_CONVERTER_H

#include <stdint.h>
#include <stddef.h>
#include <cJSON.h>

// 错误码定义
#define CONVERTER_SUCCESS           0
#define CONVERTER_ERROR_INVALID_ARG -1
#define CONVERTER_ERROR_OVERFLOW    -2
#define CONVERTER_ERROR_FORMAT      -3
#define CONVERTER_ERROR_NOT_FOUND   -4

// 24位ID处理工具 (与deprecated模块兼容)
uint32_t data_get_24bit_value(uint32_t raw_value);
int data_validate_and_convert_24bit(const cJSON *json, const char *field, uint32_t *output);

// IP地址转换 (安全接口，替代直接的inet_addr/inet_ntoa)
uint32_t ip_str_to_uint32_safe(const char *ip_str);
int ip_uint32_to_str_safe(uint32_t ip_num, char *buffer, size_t buffer_size);
uint32_t ip_str_to_uint32_host_safe(const char *ip_str);
int ip_uint32_host_to_str_safe(uint32_t ip_host, char *buffer, size_t buffer_size);

// JSON字段安全提取 (统一错误处理)
int json_get_uint32_safe(const cJSON *json, const char *field, uint32_t *value);
int json_get_uint16_safe(const cJSON *json, const char *field, uint16_t *value);
int json_get_uint8_safe(const cJSON *json, const char *field, uint8_t *value);
int json_get_string_safe(const cJSON *json, const char *field, char *buffer, size_t size);
int json_get_ip_safe(const cJSON *json, const char *field, uint32_t *ip_value);

// 24位ID的JSON处理
int json_get_24bit_id_safe(const cJSON *json, const char *field, uint32_t *id_value);
int json_set_24bit_id_safe(cJSON *json, const char *field, uint32_t id_value);

// IP地址的JSON处理
int json_get_ip_string_safe(const cJSON *json, const char *field, char *ip_buffer, size_t buffer_size);
int json_set_ip_from_uint32_safe(cJSON *json, const char *field, uint32_t ip_value);
int json_set_ip_from_string_safe(cJSON *json, const char *field, const char *ip_str);

// 数据类型转换工具
int convert_hex_string_to_uint16(const char *hex_str, uint16_t *value);
int convert_uint16_to_hex_string(uint16_t value, char *hex_str, size_t size);
int convert_hex_string_to_uint32(const char *hex_str, uint32_t *value);
int convert_uint32_to_hex_string(uint32_t value, char *hex_str, size_t size);

// MAC地址转换
int convert_mac_string_to_bytes(const char *mac_str, uint8_t mac[6]);
int convert_mac_bytes_to_string(const uint8_t mac[6], char *mac_str, size_t size);

// 端口号验证和转换
int convert_and_validate_port(const cJSON *json, const char *field, uint16_t *port);
int convert_and_validate_voice_port(const cJSON *json, const char *field, uint16_t *port);
int convert_and_validate_data_port(const cJSON *json, const char *field, uint16_t *port);

// 禁用直接使用inet_addr/inet_ntoa的宏定义 (编译时检查)
#ifdef WEBCFG_STRICT_MODE
#define inet_addr(...) use_ip_str_to_uint32_safe_instead_of_inet_addr
#define inet_ntoa(...) use_ip_uint32_to_str_safe_instead_of_inet_ntoa
#endif

#endif // WEBCFG_DATA_CONVERTER_H 